<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格滚动条和文字截断测试 - Gantt Chart</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-group label {
            font-weight: 500;
            color: #333;
        }
        
        input[type="checkbox"] {
            transform: scale(1.2);
        }
        
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .gantt-container {
            padding: 20px;
            height: 600px;
        }
        
        .status {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px;
            color: #1565c0;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #0d47a1;
        }
        
        .info-box ul {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }
        
        .info-box li {
            margin: 5px 0;
        }
        
        .test-section {
            display: flex;
            gap: 20px;
            margin: 20px;
        }
        
        .test-item {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .test-header {
            background: #f8f9fa;
            padding: 10px;
            font-weight: 600;
            border-bottom: 1px solid #ddd;
        }
        
        .test-content {
            height: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 表格滚动条和文字截断测试</h1>
            <p>测试表格的滚动条显示/隐藏和文字截断功能</p>
        </div>
        
        <div class="info-box">
            <h3>测试功能：</h3>
            <ul>
                <li><strong>滚动条控制</strong>：默认隐藏滚动条，可通过配置开启</li>
                <li><strong>表格头部高度</strong>：永远与甘特图头部高度一致</li>
                <li><strong>文字截断</strong>：当文字过长时显示省略号，鼠标悬停显示完整内容</li>
                <li><strong>动态配置</strong>：可以实时切换滚动条和文字截断设置</li>
            </ul>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="showScrollbar">
                    <input type="checkbox" id="showScrollbar"> 显示滚动条
                </label>
            </div>
            
            <div class="control-group">
                <label for="textEllipsis">
                    <input type="checkbox" id="textEllipsis" checked> 启用文字截断
                </label>
            </div>
            
            <div class="control-group">
                <label for="tableWidth">表格宽度:</label>
                <select id="tableWidth">
                    <option value="200">窄 (200px)</option>
                    <option value="300" selected>中等 (300px)</option>
                    <option value="400">宽 (400px)</option>
                </select>
            </div>
            
            <button onclick="updateGanttConfig()">应用配置</button>
            <button onclick="addLongTextTasks()">添加长文本任务</button>
        </div>
        
        <div class="test-section">
            <div class="test-item">
                <div class="test-header">默认配置（隐藏滚动条）</div>
                <div class="test-content">
                    <div id="gantt1"></div>
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-header">显示滚动条配置</div>
                <div class="test-content">
                    <div id="gantt2"></div>
                </div>
            </div>
        </div>
        
        <div class="status" id="status">
            正在初始化甘特图...
        </div>
    </div>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt1 = null;
        let gantt2 = null;
        
        // 生成测试任务数据（包含长文本）
        function generateTestTasks() {
            const longTexts = [
                "这是一个非常长的任务名称，用来测试文字截断功能是否正常工作",
                "Project Management and Implementation Phase One",
                "用户界面设计和用户体验优化工作",
                "Database Design and Architecture Planning",
                "前端开发和后端API接口开发集成测试",
                "Quality Assurance and Testing Phase",
                "部署和上线准备工作以及文档编写",
                "Performance Optimization and Bug Fixes"
            ];
            
            const tasks = [];
            const baseDate = new Date();
            baseDate.setHours(0, 0, 0, 0);
            
            for (let i = 0; i < 15; i++) {
                const startOffset = Math.floor(Math.random() * 20) + i;
                const duration = Math.floor(Math.random() * 8) + 3;
                
                const start = new Date(baseDate);
                start.setDate(start.getDate() + startOffset);
                
                const end = new Date(start);
                end.setDate(end.getDate() + duration);
                
                tasks.push({
                    id: `task-${i + 1}`,
                    name: i < longTexts.length ? longTexts[i] : `任务 ${i + 1}`,
                    start: start.toISOString().split('T')[0],
                    end: end.toISOString().split('T')[0],
                    progress: Math.floor(Math.random() * 100),
                    priority: ['高', '中', '低'][Math.floor(Math.random() * 3)],
                    assignee: ['张三', '李四', '王五', 'John', 'Alice'][Math.floor(Math.random() * 5)]
                });
            }
            
            return tasks;
        }
        
        // 更新状态信息
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 初始化甘特图
        function initializeGantts() {
            const tasks = generateTestTasks();
            
            // 甘特图1：默认配置（隐藏滚动条）
            gantt1 = new Gantt("#gantt1", tasks, {
                view_mode: 'Day',
                bar_height: 28,
                bar_corner_radius: 4,
                padding: 15,
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 300,
                    show_scrollbar: false, // 隐藏滚动条
                    text_ellipsis: true,   // 启用文字截断
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 80 },
                        { key: 'priority', title: '优先级', width: 70 }
                    ]
                }
            });
            
            // 甘特图2：显示滚动条配置
            gantt2 = new Gantt("#gantt2", tasks, {
                view_mode: 'Day',
                bar_height: 28,
                bar_corner_radius: 4,
                padding: 15,
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 300,
                    show_scrollbar: true,  // 显示滚动条
                    text_ellipsis: true,   // 启用文字截断
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 80 },
                        { key: 'priority', title: '优先级', width: 70 }
                    ]
                }
            });
            
            updateStatus('甘特图已初始化。左侧隐藏滚动条，右侧显示滚动条。尝试滚动表格查看效果！');
        }
        
        // 更新甘特图配置
        function updateGanttConfig() {
            const showScrollbar = document.getElementById('showScrollbar').checked;
            const textEllipsis = document.getElementById('textEllipsis').checked;
            const tableWidth = parseInt(document.getElementById('tableWidth').value);
            
            const tasks = generateTestTasks();
            
            // 重新创建甘特图1
            document.getElementById('gantt1').innerHTML = '';
            gantt1 = new Gantt("#gantt1", tasks, {
                view_mode: 'Day',
                bar_height: 28,
                bar_corner_radius: 4,
                padding: 15,
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: tableWidth,
                    show_scrollbar: showScrollbar,
                    text_ellipsis: textEllipsis,
                    columns: [
                        { key: 'name', title: '任务名称', width: Math.floor(tableWidth * 0.5) },
                        { key: 'assignee', title: '负责人', width: Math.floor(tableWidth * 0.27) },
                        { key: 'priority', title: '优先级', width: Math.floor(tableWidth * 0.23) }
                    ]
                }
            });
            
            updateStatus(`配置已更新：滚动条=${showScrollbar ? '显示' : '隐藏'}，文字截断=${textEllipsis ? '启用' : '禁用'}，表格宽度=${tableWidth}px`);
        }
        
        // 添加长文本任务
        function addLongTextTasks() {
            const longTasks = [
                {
                    id: 'long-task-1',
                    name: '这是一个超级超级超级长的任务名称，用来测试文字截断功能在极端情况下的表现',
                    start: '2024-05-01',
                    end: '2024-05-10',
                    progress: 50,
                    priority: '高',
                    assignee: '具有很长名字的负责人'
                }
            ];
            
            // 更新甘特图1
            if (gantt1) {
                gantt1.refresh(longTasks);
            }
            
            updateStatus('已添加长文本任务，观察文字截断效果');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeGantts();
        });
    </script>
</body>
</html>
