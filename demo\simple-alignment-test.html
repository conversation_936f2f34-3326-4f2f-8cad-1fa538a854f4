<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单对齐测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .debug-line {
            position: fixed;
            height: 2px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0.8;
        }
        
        .debug-line::after {
            content: attr(data-label);
            position: absolute;
            right: 5px;
            top: -20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>简单对齐测试</h1>
    
    <button onclick="checkAlignment()">检查对齐</button>
    <button onclick="clearDebugLines()">清除调试线</button>
    
    <div id="status" class="status">
        点击"检查对齐"开始测试...
    </div>
    
    <svg id="gantt"></svg>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        // 简单的测试数据
        const tasks = [
            {
                id: 'task1',
                name: '任务1',
                start: '2024-01-05',
                end: '2024-01-12',
                progress: 100
            },
            {
                id: 'task2',
                name: '任务2',
                start: '2024-01-08',
                end: '2024-01-15',
                progress: 60
            },
            {
                id: 'task3',
                name: '任务3',
                start: '2024-01-10',
                end: '2024-01-20',
                progress: 30
            }
        ];
        
        // 初始化甘特图
        let gantt;
        try {
            const GanttConstructor = window.Gantt || window.FrappeGantt;
            console.log('Gantt Constructor:', GanttConstructor);
            
            gantt = new GanttConstructor('#gantt', tasks, {
                header_height: 50,
                column_width: 30,
                step: 24,
                bar_height: 20,
                padding: 18,
                view_mode: 'Day',
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 200,
                    columns: [
                        { key: 'name', label: '任务名称', width: 200 }
                    ]
                }
            });
            
            console.log('甘特图初始化成功');
        } catch (error) {
            console.error('甘特图初始化失败:', error);
            document.getElementById('status').innerHTML = '❌ 甘特图初始化失败: ' + error.message;
        }
        
        function checkAlignment() {
            console.log('开始检查对齐...');
            
            const tableRows = document.querySelectorAll('.table-row');
            const ganttBars = document.querySelectorAll('.bar-wrapper');
            
            console.log('表格行数:', tableRows.length);
            console.log('甘特图任务条数:', ganttBars.length);
            
            if (tableRows.length === 0 || ganttBars.length === 0) {
                console.log('等待元素加载...');
                setTimeout(checkAlignment, 500);
                return;
            }

            let maxDiff = 0;
            let alignmentIssues = [];

            // 获取容器信息
            const ganttContainer = document.querySelector('.gantt-container');
            const ganttSvg = document.querySelector('.gantt');
            
            if (!ganttContainer || !ganttSvg) {
                console.error('找不到甘特图容器或SVG元素');
                return;
            }
            
            const containerRect = ganttContainer.getBoundingClientRect();
            const svgRect = ganttSvg.getBoundingClientRect();
            
            console.log('容器位置:', containerRect);
            console.log('SVG位置:', svgRect);
            console.log('SVG偏移:', {
                x: svgRect.left - containerRect.left,
                y: svgRect.top - containerRect.top
            });

            // 清除之前的调试线
            clearDebugLines();

            for (let i = 0; i < Math.min(tableRows.length, ganttBars.length); i++) {
                const tableRow = tableRows[i];
                const ganttBar = ganttBars[i];
                
                const tableRect = tableRow.getBoundingClientRect();
                const tableCenterY = tableRect.top + tableRect.height / 2;
                
                const barRect = ganttBar.getBoundingClientRect();
                const barCenterY = barRect.top + barRect.height / 2;
                
                const diff = Math.abs(tableCenterY - barCenterY);
                maxDiff = Math.max(maxDiff, diff);
                
                console.log(`任务 ${i}:`);
                console.log(`  表格行中心Y: ${tableCenterY.toFixed(1)}`);
                console.log(`  甘特图条中心Y: ${barCenterY.toFixed(1)}`);
                console.log(`  差异: ${diff.toFixed(1)}px`);
                
                if (diff > 1) {
                    alignmentIssues.push({
                        index: i,
                        tableCenterY,
                        barCenterY,
                        diff
                    });
                }
                
                // 绘制调试线
                drawDebugLine(0, tableCenterY, window.innerWidth, tableCenterY, 'red', `表格行${i}`);
                drawDebugLine(0, barCenterY, window.innerWidth, barCenterY, 'blue', `甘特图条${i}`);
            }

            updateStatus(maxDiff, alignmentIssues);
        }
        
        function drawDebugLine(x1, y1, x2, y2, color, label) {
            const line = document.createElement('div');
            line.className = 'debug-line';
            line.style.left = x1 + 'px';
            line.style.top = y1 + 'px';
            line.style.width = (x2 - x1) + 'px';
            line.style.backgroundColor = color;
            line.setAttribute('data-label', label);
            document.body.appendChild(line);
        }
        
        function clearDebugLines() {
            document.querySelectorAll('.debug-line').forEach(line => line.remove());
        }
        
        function updateStatus(maxDiff, issues) {
            const statusEl = document.getElementById('status');
            
            if (maxDiff <= 1) {
                statusEl.className = 'status success';
                statusEl.innerHTML = '✅ 对齐完美！最大差异: ' + maxDiff.toFixed(1) + 'px';
            } else {
                statusEl.className = 'status error';
                statusEl.innerHTML = '❌ 存在对齐问题，最大差异: ' + maxDiff.toFixed(1) + 'px';
                
                if (issues.length > 0) {
                    statusEl.innerHTML += '<br><br>问题详情:<br>';
                    issues.forEach(issue => {
                        statusEl.innerHTML += `任务${issue.index}: 差异${issue.diff.toFixed(1)}px<br>`;
                    });
                }
            }
        }
        
        // 页面加载完成后自动检查对齐
        setTimeout(checkAlignment, 1000);
    </script>
</body>
</html>
