@import './light.css';

.gantt-container {
    line-height: 14.5px;
    position: relative;
    overflow: auto;
    font-size: 12px;
    height: var(--gv-grid-height);
    width: 100%;
    border-radius: 8px;
    /* 优化滚动性能 */
    will-change: scroll-position;
    transform: translateZ(0);

    & .popup-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        background: #fff;
        box-shadow: 0px 10px 24px -3px rgba(0, 0, 0, 0.2);
        padding: 10px;
        border-radius: 5px;
        width: max-content;
        z-index: 1000;

        & .title {
            margin-bottom: 2px;
            color: var(--g-text-dark);
            font-size: 0.85rem;
            font-weight: 650;
            line-height: 15px;
        }

        & .subtitle {
            color: var(--g-text-dark);
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        & .details {
            color: var(--g-text-muted);
            font-size: 0.7rem;
        }

        & .actions {
            margin-top: 10px;
            margin-left: 3px;
        }

        & .action-btn {
            border: none;
            padding: 5px 8px;
            background-color: var(--g-popup-actions);
            border-right: 1px solid var(--g-text-light);

            &:hover {
                background-color: brightness(97%);
            }

            &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
            }

            &:last-child {
                border-right: none;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }
    }

    & .grid-header {
        height: calc(
            var(--gv-lower-header-height) + var(--gv-upper-header-height) + 10px
        );
        background-color: var(--g-header-background);
        position: sticky;
        top: 0;
        left: 0;
        border-bottom: 1px solid var(--g-row-border-color);
        z-index: 1000;
    }

    & .lower-text,
    & .upper-text {
        text-anchor: middle;
    }

    & .upper-header {
        height: var(--gv-upper-header-height);
    }

    & .lower-header {
        height: var(--gv-lower-header-height);
    }

    & .lower-text {
        font-size: 12px;
        position: absolute;
        width: calc(var(--gv-column-width) * 0.8);
        height: calc(var(--gv-lower-header-height) * 0.8);
        margin: 0 calc(var(--gv-column-width) * 0.1);
        align-content: center;
        text-align: center;
        color: var(--g-text-muted);
    }

    & .upper-text {
        position: absolute;
        width: fit-content;
        font-weight: 500;
        font-size: 14px;
        color: var(--g-text-dark);
        height: calc(var(--gv-lower-header-height) * 0.66);
    }

    & .current-upper {
        position: sticky;
        left: 0 !important;
        padding-left: 17px;
        background: white;
    }

    & .side-header {
        position: sticky;
        top: 0;
        right: 0;
        float: right;

        z-index: 1000;
        line-height: 20px;
        font-weight: 400;
        width: max-content;
        margin-left: auto;
        padding-right: 10px;
        padding-top: 10px;
        background: var(--g-header-background);
        display: flex;
    }

    & .side-header * {
        transition-property: background-color;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 150ms;
        background-color: var(--g-actions-background);
        border-radius: 0.5rem;
        border: none;
        padding: 5px 8px;
        color: var(--g-text-dark);
        font-size: 14px;
        letter-spacing: 0.02em;
        font-weight: 420;
        box-sizing: content-box;

        margin-right: 5px;

        &:last-child {
            margin-right: 0;
        }

        &:hover {
            filter: brightness(97.5%);
        }
    }

    & .side-header select {
        width: 60px;
        padding-top: 2px;
        padding-bottom: 2px;
    }
    & .side-header select:focus {
        outline: none;
    }

    /* 表格树样式 */
    & .table-tree {
        position: absolute;
        top: 0;
        left: 0;
        background: var(--g-header-background);
        border-right: 1px solid var(--g-border-color);
        z-index: 1001;
        overflow: hidden;
        height: 100%;
        pointer-events: auto;
        /* 确保表格不会随着水平滚动而移动 */
        transform: translateX(0);
        /* 确保表格树不会影响甘特图的渲染 */
        will-change: transform;
        backface-visibility: hidden;
    }

    & .table-header {
        position: relative;
        background: var(--g-header-background);
        border-bottom: 1px solid var(--g-border-color);
        z-index: 1002;
    }

    & .table-header-cell {
        position: absolute;
        display: flex;
        align-items: center;
        padding: 0 8px;
        font-weight: 600;
        font-size: 12px;
        color: var(--g-text-dark);
        border-right: 1px solid var(--g-border-color);
        background: var(--g-header-background);
    }

    & .table-body {
        position: relative;
        overflow-y: auto;
        /* 默认隐藏滚动条 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    & .table-body::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }

    /* 当配置显示滚动条时 */
    & .table-tree.show-scrollbar .table-body {
        scrollbar-width: thin; /* Firefox */
        -ms-overflow-style: auto; /* IE and Edge */
    }

    & .table-tree.show-scrollbar .table-body::-webkit-scrollbar {
        display: block; /* Chrome, Safari, Opera */
        width: 8px;
    }

    & .table-tree.show-scrollbar .table-body::-webkit-scrollbar-track {
        background: var(--g-row-color);
        border-radius: 4px;
    }

    & .table-tree.show-scrollbar .table-body::-webkit-scrollbar-thumb {
        background: var(--g-border-color);
        border-radius: 4px;
    }

    & .table-tree.show-scrollbar .table-body::-webkit-scrollbar-thumb:hover {
        background: var(--g-text-muted);
    }

    & .table-row {
        position: absolute;
        border-bottom: 1px solid var(--g-row-border-color);
        background: var(--g-row-color);

        &:hover {
            background: var(--g-row-hover-color, #f8f9fa);
        }
    }

    & .table-cell {
        position: absolute;
        display: flex;
        align-items: center;
        border-right: 1px solid var(--g-border-color);
        overflow: hidden;
    }

    & .cell-content {
        position: absolute;
        display: flex;
        align-items: center;
        padding: 0 4px;
        font-size: 12px;
        color: var(--g-text-dark);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        box-sizing: border-box;
    }

    /* 当禁用文字截断时 */
    & .table-tree.no-ellipsis .cell-content {
        text-overflow: clip;
        white-space: normal;
        word-wrap: break-word;
    }

    & .tree-toggle {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 10px;
        color: var(--g-text-muted);
        user-select: none;

        &:hover {
            color: var(--g-text-dark);
            background: var(--g-row-hover-color, #f0f0f0);
            border-radius: 2px;
        }
    }

    & .date-range-highlight {
        background-color: var(--g-progress-color);
        border-radius: 12px;
        height: calc(var(--gv-lower-header-height) - 6px);
        top: calc(var(--gv-upper-header-height) + 5px);
        position: absolute;
    }

    & .current-highlight {
        position: absolute;
        background: var(--g-today-highlight);
        width: 1px;
        z-index: 999;
    }

    & .current-ball-highlight {
        position: absolute;
        background: var(--g-today-highlight);
        z-index: 1001;
        border-radius: 50%;
    }

    & .current-date-highlight {
        background: var(--g-today-highlight);
        color: var(--g-text-light);
        border-radius: 5px;
    }

    & .holiday-label {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        z-index: 1000;
        background: --g-weekend-label-color;
        border-radius: 5px;
        padding: 2px 5px;

        &.show {
            opacity: 100;
        }
    }

    & .extras {
        position: sticky;
        left: 0px;

        & .adjust {
            position: absolute;
            left: 8px;
            top: calc(var(--gv-grid-height) - 60px);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 3px;
        }
    }

    .hide {
        display: none;
    }
}

.gantt {
    user-select: none;
    -webkit-user-select: none;
    position: absolute;
    /* 确保甘特图在滚动时不会被截断 */
    overflow: visible;
    transform: translateZ(0);

    & .grid-background {
        fill: none;
    }

    & .grid-row {
        fill: var(--g-row-color);
    }

    & .row-line {
        stroke: var(--g-border-color);
    }

    & .tick {
        stroke: var(--g-tick-color);
        stroke-width: 0.4;

        &.thick {
            stroke: var(--g-tick-color-thick);
            stroke-width: 0.7;
        }
    }

    & .arrow {
        fill: none;
        stroke: var(--g-arrow-color);
        stroke-width: 1.5;
    }

    & .bar-wrapper .bar {
        fill: var(--g-bar-color);
        stroke: var(--g-bar-border);
        stroke-width: 0;
        transition: stroke-width 0.3s ease;
    }

    & .bar-progress {
        fill: var(--g-progress-color);
        border-radius: 4px;
    }

    & .bar-expected-progress {
        fill: var(--g-expected-progress);
    }

    & .bar-invalid {
        fill: transparent;
        stroke: var(--g-bar-border);
        stroke-width: 1;
        stroke-dasharray: 5;

        & ~ .bar-label {
            fill: var(--g-text-light);
        }
    }

    & .bar-label {
        fill: var(--g-text-dark);
        dominant-baseline: central;
        font-family: Helvetica;
        font-size: 13px;
        font-weight: 400;

        &.big {
            fill: var(--g-text-dark);
            text-anchor: start;
        }
    }

    & .handle {
        fill: var(--g-handle-color);
        opacity: 0;
        transition: opacity 0.3s ease;
        &.active,
        &.visible {
            cursor: ew-resize;
            opacity: 1;
        }
    }

    & .handle.progress {
        fill: var(--g-text-muted);
    }

    & .bar-wrapper {
            cursor: pointer;

        & .bar {
            outline: 1px solid var(--g-row-border-color);
            border-radius: 3px;
        }

        &:hover {
            .bar {
                transition: transform 0.3s ease;
            }

            .date-range-highlight {
                display: block;
            }
        }
    }
}
