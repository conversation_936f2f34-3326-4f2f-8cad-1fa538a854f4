<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动修复测试 - Gantt Chart</title>
       <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-group label {
            font-weight: 500;
            color: #333;
        }
        
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .gantt-container {
            padding: 20px;
            height: 500px;
        }
        
        .status {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px;
            color: #1565c0;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #0d47a1;
        }
        
        .info-box ul {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }
        
        .info-box li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 滚动修复测试</h1>
            <p>测试鼠标滚动时空白日期的优化效果</p>
        </div>
        
        <div class="info-box">
            <h3>修复内容：</h3>
            <ul>
                <li><strong>智能扩展触发</strong>：只在真正需要时扩展日期范围</li>
                <li><strong>减少扩展单位</strong>：从10个单位减少到5个单位</li>
                <li><strong>任务边界检查</strong>：基于实际任务时间范围判断是否需要扩展</li>
                <li><strong>动态padding计算</strong>：根据视图模式和任务数量智能调整初始padding</li>
                <li><strong>增强防抖机制</strong>：防止频繁触发扩展操作</li>
            </ul>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="viewMode">视图模式:</label>
                <select id="viewMode">
                    <option value="Hour">小时</option>
                    <option value="Quarter Day">6小时</option>
                    <option value="Half Day">半天</option>
                    <option value="Day" selected>天</option>
                    <option value="Week">周</option>
                    <option value="Month">月</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="taskCount">任务数量:</label>
                <select id="taskCount">
                    <option value="3">少量任务 (3个)</option>
                    <option value="8" selected>中等任务 (8个)</option>
                    <option value="15">较多任务 (15个)</option>
                </select>
            </div>
            
            <button onclick="regenerateGantt()">重新生成甘特图</button>
            <button onclick="scrollToStart()">滚动到开始</button>
            <button onclick="scrollToEnd()">滚动到结束</button>
        </div>
        
        <div class="gantt-container">
            <div id="gantt"></div>
        </div>
        
        <div class="status" id="status">
            正在初始化甘特图...
        </div>
    </div>

    <!-- <script src="../dist/frappe-gantt.js"></script> -->
      <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt = null;
        
        // 生成测试任务数据
        function generateTasks(count) {
            const tasks = [];
            const baseDate = new Date();
            baseDate.setHours(0, 0, 0, 0);
            
            for (let i = 0; i < count; i++) {
                const startOffset = Math.floor(Math.random() * 30) + i * 2; // 0-30天的随机偏移
                const duration = Math.floor(Math.random() * 10) + 3; // 3-12天的持续时间
                
                const start = new Date(baseDate);
                start.setDate(start.getDate() + startOffset);
                
                const end = new Date(start);
                end.setDate(end.getDate() + duration);
                
                tasks.push({
                    id: `task-${i + 1}`,
                    name: `任务 ${i + 1}`,
                    start: start.toISOString().split('T')[0],
                    end: end.toISOString().split('T')[0],
                    progress: Math.floor(Math.random() * 100),
                    dependencies: i > 0 && Math.random() > 0.7 ? `task-${i}` : ''
                });
            }
            
            return tasks;
        }
        
        // 更新状态信息
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 重新生成甘特图
        function regenerateGantt() {
            const viewMode = document.getElementById('viewMode').value;
            const taskCount = parseInt(document.getElementById('taskCount').value);
            
            updateStatus('正在生成新的甘特图...');
            
            const tasks = generateTasks(taskCount);
            
            // 销毁现有甘特图
            if (gantt) {
                document.getElementById('gantt').innerHTML = '';
            }
            
            // 创建新甘特图
            gantt = new Gantt("#gantt", tasks, {
                view_mode: viewMode,
                bar_height: 30,
                bar_corner_radius: 6,
                arrow_curve: 8,
                padding: 18,
                infinite_padding: true, // 启用无限滚动
                // 表格树配置
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 250,
                    columns: [
                        { key: 'name', title: '任务名称', width: 120 },
                        { key: 'start', title: '开始时间', width: 80 },
                        { key: 'progress', title: '进度', width: 50 }
                    ]
                },
                on_date_change: function(task, start, end) {
                    updateStatus(`任务 ${task.name} 时间已更新`);
                },
                on_progress_change: function(task, progress) {
                    updateStatus(`任务 ${task.name} 进度更新为 ${progress}%`);
                }
            });
            
            updateStatus(`甘特图已生成 - ${taskCount}个任务，${viewMode}视图模式。尝试滚动查看优化效果！`);
        }
        
        // 滚动到开始位置
        function scrollToStart() {
            if (gantt) {
                gantt.set_scroll_position('start');
                updateStatus('已滚动到开始位置');
            }
        }
        
        // 滚动到结束位置
        function scrollToEnd() {
            if (gantt) {
                gantt.set_scroll_position('end');
                updateStatus('已滚动到结束位置');
            }
        }
        
        // 视图模式改变事件
        document.getElementById('viewMode').addEventListener('change', function() {
            if (gantt) {
                gantt.change_view_mode(this.value);
                updateStatus(`视图模式已切换到: ${this.value}`);
            }
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            regenerateGantt();
        });
    </script>
</body>
</html>
