<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精确对齐测试</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #2196F3;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .gantt-container {
            padding: 20px;
            height: 500px;
            position: relative;
            overflow: hidden;
        }
        
        .debug-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 300px;
        }
        
        .debug-info h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        .debug-line {
            margin: 5px 0;
        }
        
        button {
            padding: 8px 16px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #1976D2;
        }
        
        .status {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
        }
        
        /* 调试样式 */
        .debug-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 9999;
        }
        
        .debug-line-horizontal {
            position: absolute;
            width: 100%;
            height: 1px;
            background: rgba(255, 0, 0, 0.5);
            border-top: 1px dashed rgba(255, 0, 0, 0.8);
        }
        
        .debug-line-vertical {
            position: absolute;
            height: 100%;
            width: 1px;
            background: rgba(0, 255, 0, 0.5);
            border-left: 1px dashed rgba(0, 255, 0, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 精确对齐测试</h1>
            <p>验证表格与甘特图的像素级对齐</p>
        </div>
        
        <div class="controls">
            <button onclick="toggleDebugMode()">切换调试模式</button>
            <button onclick="measureAlignment()">测量对齐</button>
            <button onclick="regenerateGantt()">重新生成</button>
            <button onclick="exportDebugInfo()">导出调试信息</button>
        </div>
        
        <div class="gantt-container">
            <div id="gantt"></div>
            <div class="debug-overlay" id="debugOverlay" style="display: none;"></div>
        </div>
        
        <div class="status" id="status">
            正在初始化甘特图...
        </div>
    </div>
    
    <div class="debug-info" id="debugInfo" style="display: none;">
        <h4>调试信息</h4>
        <div id="debugContent"></div>
    </div>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt = null;
        let debugMode = false;
        
        // 生成测试数据
        function generateTestData() {
            const tasks = [];
            const baseDate = new Date('2024-07-01');
            
            const taskNames = [
                "项目启动和需求分析",
                "系统架构设计", 
                "前端界面开发",
                "后端API开发",
                "数据库设计",
                "集成测试"
            ];
            
            taskNames.forEach((name, i) => {
                const start = new Date(baseDate);
                start.setDate(start.getDate() + i * 4);
                
                const end = new Date(start);
                end.setDate(end.getDate() + 6);
                
                tasks.push({
                    id: `task-${i + 1}`,
                    name: name,
                    start: start.toISOString().split('T')[0],
                    end: end.toISOString().split('T')[0],
                    progress: Math.floor(Math.random() * 100),
                    assignee: ['张三', '李四', '王五', 'Alice', 'Bob'][i % 5],
                    priority: ['高', '中', '低'][i % 3]
                });
            });
            
            return tasks;
        }
        
        // 初始化甘特图
        function initializeGantt() {
            const tasks = generateTestData();
            
            if (gantt) {
                document.getElementById('gantt').innerHTML = '';
            }
            
            gantt = new Gantt("#gantt", tasks, {
                view_mode: 'Day',
                bar_height: 30,
                bar_corner_radius: 6,
                arrow_curve: 8,
                padding: 18,
                upper_header_height: 45,
                lower_header_height: 30,
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 300,
                    show_scrollbar: false,
                    text_ellipsis: true,
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 80 },
                        { key: 'priority', title: '优先级', width: 70 }
                    ]
                }
            });
            
            updateStatus('甘特图已初始化，点击"切换调试模式"查看对齐情况');
            
            // 延迟测量，确保DOM完全渲染
            setTimeout(() => {
                if (debugMode) {
                    measureAlignment();
                }
            }, 500);
        }
        
        // 切换调试模式
        function toggleDebugMode() {
            debugMode = !debugMode;
            const debugOverlay = document.getElementById('debugOverlay');
            const debugInfo = document.getElementById('debugInfo');
            
            if (debugMode) {
                debugOverlay.style.display = 'block';
                debugInfo.style.display = 'block';
                generateDebugLines();
                measureAlignment();
            } else {
                debugOverlay.style.display = 'none';
                debugInfo.style.display = 'none';
            }
        }
        
        // 生成调试参考线
        function generateDebugLines() {
            const overlay = document.getElementById('debugOverlay');
            overlay.innerHTML = '';
            
            if (!gantt) return;
            
            const container = document.querySelector('.gantt-container');
            const containerRect = container.getBoundingClientRect();
            
            // 获取甘特图配置
            const headerHeight = gantt.config.header_height;
            const barHeight = gantt.options.bar_height;
            const padding = gantt.options.padding;
            
            // 头部分割线
            const headerLine = document.createElement('div');
            headerLine.className = 'debug-line-horizontal';
            headerLine.style.top = (headerHeight + 20) + 'px';
            headerLine.style.background = 'rgba(0, 255, 0, 0.8)';
            overlay.appendChild(headerLine);
            
            // 任务行参考线
            for (let i = 0; i < 6; i++) {
                // 甘特图任务条Y坐标计算
                const taskY = headerHeight + 20 + padding / 2 + i * (barHeight + padding);
                
                const taskLine = document.createElement('div');
                taskLine.className = 'debug-line-horizontal';
                taskLine.style.top = taskY + 'px';
                taskLine.style.background = 'rgba(255, 0, 0, 0.6)';
                overlay.appendChild(taskLine);
                
                // 任务条中心线
                const centerLine = document.createElement('div');
                centerLine.className = 'debug-line-horizontal';
                centerLine.style.top = (taskY + barHeight / 2) + 'px';
                centerLine.style.background = 'rgba(0, 0, 255, 0.4)';
                overlay.appendChild(centerLine);
            }
            
            // 表格分割线
            const tableLine = document.createElement('div');
            tableLine.className = 'debug-line-vertical';
            tableLine.style.left = '320px'; // 300px + 20px padding
            tableLine.style.background = 'rgba(255, 165, 0, 0.8)';
            overlay.appendChild(tableLine);
        }
        
        // 测量对齐精度
        function measureAlignment() {
            if (!gantt) return;
            
            const measurements = {
                headerHeight: gantt.config.header_height,
                barHeight: gantt.options.bar_height,
                padding: gantt.options.padding,
                tableWidth: gantt.options.table_tree.table_width
            };
            
            // 获取DOM元素
            const tableHeader = document.querySelector('.table-header');
            const tableRows = document.querySelectorAll('.table-row');
            const gantBars = document.querySelectorAll('.bar-wrapper');
            
            if (tableHeader) {
                measurements.tableHeaderHeight = tableHeader.offsetHeight;
                measurements.tableHeaderTop = tableHeader.offsetTop;
            }
            
            if (tableRows.length > 0) {
                measurements.tableRowHeight = tableRows[0].offsetHeight;
                measurements.tableRowPositions = Array.from(tableRows).map(row => ({
                    top: row.offsetTop,
                    height: row.offsetHeight
                }));
            }
            
            if (gantBars.length > 0) {
                measurements.barPositions = Array.from(gantBars).map(bar => {
                    const rect = bar.getBoundingClientRect();
                    const container = document.querySelector('.gantt-container');
                    const containerRect = container.getBoundingClientRect();
                    return {
                        top: rect.top - containerRect.top,
                        height: rect.height
                    };
                });
            }
            
            displayDebugInfo(measurements);
        }
        
        // 显示调试信息
        function displayDebugInfo(measurements) {
            const debugContent = document.getElementById('debugContent');
            
            let html = `
                <div class="debug-line">头部高度: ${measurements.headerHeight}px</div>
                <div class="debug-line">任务条高度: ${measurements.barHeight}px</div>
                <div class="debug-line">行间距: ${measurements.padding}px</div>
                <div class="debug-line">表格宽度: ${measurements.tableWidth}px</div>
            `;
            
            if (measurements.tableHeaderHeight) {
                html += `<div class="debug-line">表格头部高度: ${measurements.tableHeaderHeight}px</div>`;
                const heightDiff = Math.abs(measurements.tableHeaderHeight - measurements.headerHeight);
                html += `<div class="debug-line">头部高度差: ${heightDiff}px ${heightDiff === 0 ? '✅' : '❌'}</div>`;
            }
            
            if (measurements.tableRowHeight) {
                html += `<div class="debug-line">表格行高度: ${measurements.tableRowHeight}px</div>`;
                const heightDiff = Math.abs(measurements.tableRowHeight - measurements.barHeight);
                html += `<div class="debug-line">行高度差: ${heightDiff}px ${heightDiff === 0 ? '✅' : '❌'}</div>`;
            }
            
            if (measurements.tableRowPositions && measurements.barPositions) {
                html += `<div class="debug-line">--- 位置对比 ---</div>`;
                const minLength = Math.min(measurements.tableRowPositions.length, measurements.barPositions.length);
                
                for (let i = 0; i < minLength; i++) {
                    const tableY = measurements.tableRowPositions[i].top;
                    const barY = measurements.barPositions[i].top;
                    const diff = Math.abs(tableY - barY);
                    
                    html += `<div class="debug-line">行${i+1}: 表格${tableY.toFixed(1)}px, 甘特${barY.toFixed(1)}px, 差值${diff.toFixed(1)}px ${diff < 1 ? '✅' : '❌'}</div>`;
                }
            }
            
            debugContent.innerHTML = html;
        }
        
        // 重新生成甘特图
        function regenerateGantt() {
            initializeGantt();
        }
        
        // 导出调试信息
        function exportDebugInfo() {
            measureAlignment();
            const debugContent = document.getElementById('debugContent').textContent;
            
            const blob = new Blob([debugContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'gantt-alignment-debug.txt';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeGantt();
        });
    </script>
</body>
</html>
