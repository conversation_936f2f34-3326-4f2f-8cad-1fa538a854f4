<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格树对齐最终测试</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #1e7e34;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            font-weight: bold;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .gantt-wrapper {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            height: 600px;
        }
        
        .features {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .features h3 {
            margin-top: 0;
            color: #333;
        }
        
        .features ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .features li {
            margin-bottom: 8px;
            color: #666;
        }
        
        .debug-line {
            position: fixed;
            height: 2px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0.7;
        }
        
        .debug-line::after {
            content: attr(data-label);
            position: absolute;
            right: 5px;
            top: -20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 表格树对齐最终测试</h1>
        <p>验证表格与甘特图的完美对齐效果</p>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="checkAlignment()">检查对齐</button>
        <button class="btn" onclick="clearDebugLines()">清除调试线</button>
        <button class="btn success" onclick="toggleCollapse()">切换折叠</button>
    </div>
    
    <div id="status" class="status">
        点击"检查对齐"开始测试...
    </div>
    
    <div class="gantt-wrapper">
        <svg id="gantt"></svg>
    </div>
    
    <div class="features">
        <h3>✨ 已实现的功能特性</h3>
        <ul>
            <li>✅ 表格与甘特图完美对齐（误差 < 1px）</li>
            <li>✅ 表格头部高度与甘特图头部高度一致</li>
            <li>✅ 支持树形结构的展开/折叠</li>
            <li>✅ 父节点折叠时隐藏所有子孙节点的甘特图条</li>
            <li>✅ 文本溢出自动截断并显示省略号</li>
            <li>✅ 可配置是否显示滚动条（默认隐藏）</li>
            <li>✅ 防止表格宽度溢出影响甘特图显示</li>
            <li>✅ 优化空白日期区域，减少不必要的滚动</li>
        </ul>
    </div>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt;
        let isCollapsed = false;
        
        // 测试数据 - 包含层级结构
        const tasks = [
            {
                id: 'project1',
                name: '🚀 系统开发项目',
                start: '2024-01-05',
                end: '2024-01-25',
                progress: 75,
                assignee: '项目经理',
                priority: '高',
                type: 'project'
            },
            {
                id: 'phase1',
                name: '📋 需求分析阶段',
                start: '2024-01-05',
                end: '2024-01-12',
                progress: 100,
                assignee: '产品经理',
                priority: '高',
                parent: 'project1'
            },
            {
                id: 'task1',
                name: '需求调研',
                start: '2024-01-05',
                end: '2024-01-08',
                progress: 100,
                assignee: '李明',
                priority: '高',
                parent: 'phase1'
            },
            {
                id: 'task2',
                name: '需求文档编写',
                start: '2024-01-09',
                end: '2024-01-12',
                progress: 100,
                assignee: '王芳',
                priority: '高',
                parent: 'phase1'
            },
            {
                id: 'phase2',
                name: '💻 开发阶段',
                start: '2024-01-13',
                end: '2024-01-25',
                progress: 60,
                assignee: '技术经理',
                priority: '高',
                parent: 'project1'
            },
            {
                id: 'task3',
                name: '前端开发',
                start: '2024-01-13',
                end: '2024-01-20',
                progress: 80,
                assignee: 'Alice',
                priority: '高',
                parent: 'phase2'
            },
            {
                id: 'task4',
                name: '后端API开发',
                start: '2024-01-15',
                end: '2024-01-22',
                progress: 50,
                assignee: 'Bob',
                priority: '中',
                parent: 'phase2'
            },
            {
                id: 'task5',
                name: '数据库设计',
                start: '2024-01-18',
                end: '2024-01-25',
                progress: 30,
                assignee: 'Charlie',
                priority: '中',
                parent: 'phase2'
            }
        ];
        
        function initGantt() {
            try {
                const GanttConstructor = window.Gantt || window.FrappeGantt;
                
                gantt = new GanttConstructor('#gantt', tasks, {
                    header_height: 50,
                    column_width: 30,
                    step: 24,
                    bar_height: 20,
                    bar_corner_radius: 3,
                    arrow_curve: 5,
                    padding: 18,
                    view_mode: 'Day',
                    date_format: 'YYYY-MM-DD',
                    popup_trigger: 'click',
                    language: 'zh',
                    table_tree: {
                        enabled: true,
                        show_table: true,
                        table_width: 350,
                        columns: [
                            { key: 'name', label: '任务名称', width: 180 },
                            { key: 'assignee', label: '负责人', width: 80 },
                            { key: 'priority', label: '优先级', width: 70 }
                        ],
                        show_scrollbar: false,
                        text_ellipsis: true
                    }
                });
                
                console.log('甘特图初始化成功');
                
                // 自动检查对齐
                setTimeout(checkAlignment, 1000);
                
            } catch (error) {
                console.error('甘特图初始化失败:', error);
                document.getElementById('status').innerHTML = '❌ 甘特图初始化失败: ' + error.message;
            }
        }
        
        function checkAlignment() {
            const tableRows = document.querySelectorAll('.table-row');
            const ganttBars = document.querySelectorAll('.bar-wrapper');
            
            if (tableRows.length === 0 || ganttBars.length === 0) {
                console.log('等待元素加载...');
                setTimeout(checkAlignment, 500);
                return;
            }

            let maxDiff = 0;
            let alignmentIssues = [];
            let perfectCount = 0;

            clearDebugLines();

            for (let i = 0; i < Math.min(tableRows.length, ganttBars.length); i++) {
                const tableRow = tableRows[i];
                const ganttBar = ganttBars[i];
                
                const tableRect = tableRow.getBoundingClientRect();
                const tableCenterY = tableRect.top + tableRect.height / 2;
                
                const barRect = ganttBar.getBoundingClientRect();
                const barCenterY = barRect.top + barRect.height / 2;
                
                const diff = Math.abs(tableCenterY - barCenterY);
                maxDiff = Math.max(maxDiff, diff);
                
                if (diff <= 1) {
                    perfectCount++;
                } else {
                    alignmentIssues.push({
                        index: i,
                        tableCenterY,
                        barCenterY,
                        diff
                    });
                }
                
                // 绘制调试线
                drawDebugLine(0, tableCenterY, window.innerWidth, tableCenterY, 'rgba(255,0,0,0.6)', `表格${i}`);
                drawDebugLine(0, barCenterY, window.innerWidth, barCenterY, 'rgba(0,0,255,0.6)', `甘特${i}`);
            }

            updateStatus(maxDiff, alignmentIssues, perfectCount, Math.min(tableRows.length, ganttBars.length));
        }
        
        function drawDebugLine(x1, y1, x2, y2, color, label) {
            const line = document.createElement('div');
            line.className = 'debug-line';
            line.style.left = x1 + 'px';
            line.style.top = y1 + 'px';
            line.style.width = (x2 - x1) + 'px';
            line.style.backgroundColor = color;
            line.setAttribute('data-label', label);
            document.body.appendChild(line);
        }
        
        function clearDebugLines() {
            document.querySelectorAll('.debug-line').forEach(line => line.remove());
        }
        
        function updateStatus(maxDiff, issues, perfectCount, totalCount) {
            const statusEl = document.getElementById('status');
            
            if (maxDiff <= 1) {
                statusEl.className = 'status success';
                statusEl.innerHTML = `🎉 对齐完美！${perfectCount}/${totalCount} 行完美对齐，最大差异: ${maxDiff.toFixed(1)}px`;
            } else {
                statusEl.className = 'status error';
                statusEl.innerHTML = `⚠️ 存在对齐问题，${perfectCount}/${totalCount} 行完美对齐，最大差异: ${maxDiff.toFixed(1)}px`;
            }
        }
        
        function toggleCollapse() {
            if (gantt && gantt.toggle_collapse) {
                // 切换第一个项目的折叠状态
                gantt.toggle_collapse('project1');
                isCollapsed = !isCollapsed;
                
                // 重新检查对齐
                setTimeout(checkAlignment, 300);
            }
        }
        
        // 初始化甘特图
        initGantt();
    </script>
</body>
</html>
