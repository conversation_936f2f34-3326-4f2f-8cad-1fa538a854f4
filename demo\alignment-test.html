<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格与甘特图对齐测试</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .control-group label {
            font-weight: 500;
            color: #333;
        }
        
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        .gantt-container {
            padding: 20px;
            height: 600px;
            position: relative;
        }
        
        .status {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
        }
        
        .info-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
            padding: 15px;
            margin: 20px;
            color: #2e7d32;
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: #1b5e20;
        }
        
        .alignment-guide {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 10000;
        }
        
        .guide-line {
            position: absolute;
            background: rgba(255, 0, 0, 0.3);
            pointer-events: none;
        }
        
        .guide-line.horizontal {
            height: 1px;
            width: 100%;
        }
        
        .guide-line.vertical {
            width: 1px;
            height: 100%;
        }
        
        .toggle-guides {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10001;
            background: #ff5722;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 表格与甘特图对齐测试</h1>
            <p>验证表格头部、行高度与甘特图的完美对齐</p>
        </div>
        
        <div class="info-box">
            <h3>✅ 修复内容：</h3>
            <ul>
                <li><strong>表格头部高度</strong>：与甘特图头部高度完全一致</li>
                <li><strong>表格行高度</strong>：与甘特图任务条行高度完全对齐</li>
                <li><strong>垂直对齐</strong>：表格行与甘特图任务条在同一水平线上</li>
                <li><strong>Y坐标计算</strong>：使用相同的计算公式确保精确对齐</li>
            </ul>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="barHeight">任务条高度:</label>
                <select id="barHeight">
                    <option value="20">小 (20px)</option>
                    <option value="25">较小 (25px)</option>
                    <option value="30" selected>默认 (30px)</option>
                    <option value="35">较大 (35px)</option>
                    <option value="40">大 (40px)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="padding">行间距:</label>
                <select id="padding">
                    <option value="10">紧密 (10px)</option>
                    <option value="15">较紧 (15px)</option>
                    <option value="18" selected>默认 (18px)</option>
                    <option value="20">较松 (20px)</option>
                    <option value="25">宽松 (25px)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="headerHeight">头部高度:</label>
                <select id="headerHeight">
                    <option value="60">小 (60px)</option>
                    <option value="70">较小 (70px)</option>
                    <option value="75" selected>默认 (75px)</option>
                    <option value="80">较大 (80px)</option>
                    <option value="90">大 (90px)</option>
                </select>
            </div>
            
            <button onclick="updateGantt()">应用设置</button>
            <button onclick="addMoreTasks()">添加更多任务</button>
        </div>
        
        <div class="gantt-container">
            <div id="gantt"></div>
            <div class="alignment-guide" id="alignmentGuide" style="display: none;">
                <!-- 对齐参考线将在这里动态生成 -->
            </div>
        </div>
        
        <div class="status" id="status">
            正在初始化甘特图...
        </div>
    </div>
    
    <button class="toggle-guides" onclick="toggleAlignmentGuides()">
        显示/隐藏对齐参考线
    </button>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt = null;
        let guidesVisible = false;
        
        // 生成测试任务数据
        function generateTasks() {
            const tasks = [];
            const baseDate = new Date();
            baseDate.setHours(0, 0, 0, 0);
            
            const taskNames = [
                "项目启动和需求分析",
                "系统架构设计",
                "前端界面开发",
                "后端API开发",
                "数据库设计",
                "集成测试",
                "用户验收测试",
                "部署上线"
            ];
            
            for (let i = 0; i < taskNames.length; i++) {
                const startOffset = i * 3 + Math.floor(Math.random() * 5);
                const duration = Math.floor(Math.random() * 8) + 5;
                
                const start = new Date(baseDate);
                start.setDate(start.getDate() + startOffset);
                
                const end = new Date(start);
                end.setDate(end.getDate() + duration);
                
                tasks.push({
                    id: `task-${i + 1}`,
                    name: taskNames[i],
                    start: start.toISOString().split('T')[0],
                    end: end.toISOString().split('T')[0],
                    progress: Math.floor(Math.random() * 100),
                    priority: ['高', '中', '低'][Math.floor(Math.random() * 3)],
                    assignee: ['张三', '李四', '王五', 'Alice', 'Bob'][Math.floor(Math.random() * 5)]
                });
            }
            
            return tasks;
        }
        
        // 更新状态信息
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        // 初始化甘特图
        function initializeGantt() {
            const barHeight = parseInt(document.getElementById('barHeight').value);
            const padding = parseInt(document.getElementById('padding').value);
            const headerHeight = parseInt(document.getElementById('headerHeight').value);
            
            const tasks = generateTasks();
            
            // 销毁现有甘特图
            if (gantt) {
                document.getElementById('gantt').innerHTML = '';
            }
            
            // 创建新甘特图
            gantt = new Gantt("#gantt", tasks, {
                view_mode: 'Day',
                bar_height: barHeight,
                bar_corner_radius: 6,
                arrow_curve: 8,
                padding: padding,
                upper_header_height: Math.floor(headerHeight * 0.6),
                lower_header_height: Math.floor(headerHeight * 0.4),
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 300,
                    show_scrollbar: false,
                    text_ellipsis: true,
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 80 },
                        { key: 'priority', title: '优先级', width: 70 }
                    ]
                }
            });
            
            updateStatus(`甘特图已更新 - 任务条高度: ${barHeight}px, 行间距: ${padding}px, 头部高度: ${headerHeight}px`);
            
            // 如果参考线可见，重新生成
            if (guidesVisible) {
                setTimeout(() => generateAlignmentGuides(), 100);
            }
        }
        
        // 更新甘特图
        function updateGantt() {
            initializeGantt();
        }
        
        // 添加更多任务
        function addMoreTasks() {
            const moreTasks = [
                {
                    id: 'extra-1',
                    name: '性能优化和调试',
                    start: '2024-06-01',
                    end: '2024-06-10',
                    progress: 30,
                    priority: '中',
                    assignee: '技术团队'
                },
                {
                    id: 'extra-2',
                    name: '文档编写和培训',
                    start: '2024-06-05',
                    end: '2024-06-15',
                    progress: 0,
                    priority: '低',
                    assignee: '产品团队'
                }
            ];
            
            if (gantt) {
                // 重新初始化包含更多任务的甘特图
                const currentTasks = generateTasks();
                const allTasks = [...currentTasks, ...moreTasks];
                
                document.getElementById('gantt').innerHTML = '';
                const barHeight = parseInt(document.getElementById('barHeight').value);
                const padding = parseInt(document.getElementById('padding').value);
                const headerHeight = parseInt(document.getElementById('headerHeight').value);
                
                gantt = new Gantt("#gantt", allTasks, {
                    view_mode: 'Day',
                    bar_height: barHeight,
                    padding: padding,
                    upper_header_height: Math.floor(headerHeight * 0.6),
                    lower_header_height: Math.floor(headerHeight * 0.4),
                    table_tree: {
                        enabled: true,
                        show_table: true,
                        table_width: 300,
                        columns: [
                            { key: 'name', title: '任务名称', width: 150 },
                            { key: 'assignee', title: '负责人', width: 80 },
                            { key: 'priority', title: '优先级', width: 70 }
                        ]
                    }
                });
                
                updateStatus('已添加更多任务，验证对齐效果');
            }
        }
        
        // 生成对齐参考线
        function generateAlignmentGuides() {
            const guide = document.getElementById('alignmentGuide');
            guide.innerHTML = '';
            
            if (!gantt) return;
            
            const container = document.querySelector('.gantt-container');
            const containerRect = container.getBoundingClientRect();
            
            // 获取甘特图配置
            const barHeight = gantt.options.bar_height;
            const padding = gantt.options.padding;
            const headerHeight = gantt.config.header_height;
            
            // 生成头部分割线
            const headerLine = document.createElement('div');
            headerLine.className = 'guide-line horizontal';
            headerLine.style.top = (headerHeight + 20) + 'px'; // 20px是容器padding
            headerLine.style.background = 'rgba(0, 255, 0, 0.5)';
            headerLine.style.height = '2px';
            guide.appendChild(headerLine);
            
            // 生成任务行参考线
            for (let i = 0; i < 8; i++) {
                const y = headerHeight + 20 + padding / 2 + i * (barHeight + padding);
                
                const rowLine = document.createElement('div');
                rowLine.className = 'guide-line horizontal';
                rowLine.style.top = y + 'px';
                rowLine.style.background = 'rgba(255, 0, 0, 0.3)';
                guide.appendChild(rowLine);
                
                // 任务条中心线
                const centerLine = document.createElement('div');
                centerLine.className = 'guide-line horizontal';
                centerLine.style.top = (y + barHeight / 2) + 'px';
                centerLine.style.background = 'rgba(0, 0, 255, 0.3)';
                guide.appendChild(centerLine);
            }
            
            // 表格分割线
            const tableLine = document.createElement('div');
            tableLine.className = 'guide-line vertical';
            tableLine.style.left = '320px'; // 300px表格宽度 + 20px容器padding
            tableLine.style.background = 'rgba(255, 165, 0, 0.5)';
            tableLine.style.width = '2px';
            guide.appendChild(tableLine);
        }
        
        // 切换对齐参考线显示
        function toggleAlignmentGuides() {
            const guide = document.getElementById('alignmentGuide');
            guidesVisible = !guidesVisible;
            
            if (guidesVisible) {
                guide.style.display = 'block';
                generateAlignmentGuides();
            } else {
                guide.style.display = 'none';
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeGantt();
        });
    </script>
</body>
</html>
