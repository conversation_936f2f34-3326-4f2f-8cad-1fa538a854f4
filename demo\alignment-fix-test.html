<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对齐修复测试</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .gantt-container {
            padding: 20px;
            height: 500px;
            position: relative;
            background: white;
        }
        
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        button:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .status {
            padding: 20px;
            background: #e9ecef;
            color: #495057;
            font-size: 14px;
            border-top: 1px solid #dee2e6;
        }
        
        .success {
            color: #28a745;
            font-weight: bold;
        }
        
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        
        /* 调试样式 */
        .debug-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            pointer-events: none;
            z-index: 9999;
            display: none;
        }
        
        .debug-line {
            position: absolute;
            width: 100%;
            height: 1px;
            background: rgba(255, 0, 0, 0.7);
            border-top: 1px dashed rgba(255, 0, 0, 0.9);
        }
        
        .debug-line.center {
            background: rgba(0, 0, 255, 0.5);
            border-top: 1px dashed rgba(0, 0, 255, 0.7);
        }
        
        .debug-line.header {
            background: rgba(0, 255, 0, 0.7);
            border-top: 2px solid rgba(0, 255, 0, 0.9);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 对齐修复测试</h1>
            <p>验证表格与甘特图的精确对齐</p>
        </div>
        
        <div class="controls">
            <button onclick="toggleDebugLines()">显示/隐藏调试线</button>
            <button onclick="checkAlignment()">检查对齐</button>
            <button onclick="refreshGantt()">刷新甘特图</button>
            <span id="alignmentStatus" class="status"></span>
        </div>
        
        <div class="gantt-container">
            <div id="gantt"></div>
            <div class="debug-overlay" id="debugOverlay"></div>
        </div>
        
        <div class="status" id="statusBar">
            正在初始化甘特图...
        </div>
    </div>

    <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt = null;
        let debugVisible = false;
        
        // 生成测试数据
        function generateTasks() {
            const tasks = [];
            const startDate = new Date('2024-07-01');
            
            const taskData = [
                { name: "项目启动和需求分析", assignee: "张三", priority: "高" },
                { name: "系统架构设计", assignee: "李四", priority: "高" },
                { name: "前端界面开发", assignee: "王五", priority: "中" },
                { name: "后端API开发", assignee: "Alice", priority: "高" },
                { name: "数据库设计", assignee: "Bob", priority: "中" },
                { name: "集成测试", assignee: "Charlie", priority: "低" }
            ];
            
            taskData.forEach((task, i) => {
                const start = new Date(startDate);
                start.setDate(start.getDate() + i * 3);
                
                const end = new Date(start);
                end.setDate(end.getDate() + 5);
                
                tasks.push({
                    id: `task-${i + 1}`,
                    name: task.name,
                    start: start.toISOString().split('T')[0],
                    end: end.toISOString().split('T')[0],
                    progress: Math.floor(Math.random() * 100),
                    assignee: task.assignee,
                    priority: task.priority
                });
            });
            
            return tasks;
        }
        
        // 初始化甘特图
        function initGantt() {
            const tasks = generateTasks();
            
            if (gantt) {
                document.getElementById('gantt').innerHTML = '';
            }
            
            gantt = new Gantt("#gantt", tasks, {
                view_mode: 'Day',
                bar_height: 30,
                bar_corner_radius: 6,
                arrow_curve: 8,
                padding: 18,
                upper_header_height: 45,
                lower_header_height: 30,
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 300,
                    show_scrollbar: false,
                    text_ellipsis: true,
                    columns: [
                        { key: 'name', title: '任务名称', width: 150 },
                        { key: 'assignee', title: '负责人', width: 80 },
                        { key: 'priority', title: '优先级', width: 70 }
                    ]
                }
            });
            
            updateStatus('甘特图初始化完成');
            
            // 延迟检查对齐
            setTimeout(() => {
                checkAlignment();
            }, 1000);
        }
        
        // 切换调试线显示
        function toggleDebugLines() {
            debugVisible = !debugVisible;
            const overlay = document.getElementById('debugOverlay');
            
            if (debugVisible) {
                overlay.style.display = 'block';
                generateDebugLines();
            } else {
                overlay.style.display = 'none';
            }
        }
        
        // 生成调试线
        function generateDebugLines() {
            const overlay = document.getElementById('debugOverlay');
            overlay.innerHTML = '';
            
            if (!gantt) return;
            
            const headerHeight = gantt.config.header_height;
            const barHeight = gantt.options.bar_height;
            const padding = gantt.options.padding;
            
            // 头部分割线
            const headerLine = document.createElement('div');
            headerLine.className = 'debug-line header';
            headerLine.style.top = headerHeight + 'px';
            overlay.appendChild(headerLine);
            
            // 任务行调试线
            for (let i = 0; i < 6; i++) {
                // 任务条顶部
                const taskTop = headerHeight + padding / 2 + i * (barHeight + padding);
                const topLine = document.createElement('div');
                topLine.className = 'debug-line';
                topLine.style.top = taskTop + 'px';
                overlay.appendChild(topLine);
                
                // 任务条中心线
                const centerLine = document.createElement('div');
                centerLine.className = 'debug-line center';
                centerLine.style.top = (taskTop + barHeight / 2) + 'px';
                overlay.appendChild(centerLine);
            }
        }
        
        // 检查对齐
        function checkAlignment() {
            if (!gantt) {
                updateStatus('甘特图未初始化', 'error');
                return;
            }
            
            const tableRows = document.querySelectorAll('.table-row');
            const barWrappers = document.querySelectorAll('.bar-wrapper');
            
            if (tableRows.length === 0 || barWrappers.length === 0) {
                updateStatus('未找到表格行或甘特图任务条', 'error');
                return;
            }
            
            let maxDiff = 0;
            let alignmentResults = [];
            
            const minLength = Math.min(tableRows.length, barWrappers.length);
            
            for (let i = 0; i < minLength; i++) {
                const tableRow = tableRows[i];
                const barWrapper = barWrappers[i];
                
                const tableRect = tableRow.getBoundingClientRect();
                const barRect = barWrapper.getBoundingClientRect();
                
                const diff = Math.abs(tableRect.top - barRect.top);
                maxDiff = Math.max(maxDiff, diff);
                
                alignmentResults.push({
                    index: i + 1,
                    tableTop: tableRect.top.toFixed(1),
                    barTop: barRect.top.toFixed(1),
                    diff: diff.toFixed(1)
                });
            }
            
            // 显示结果
            const statusElement = document.getElementById('alignmentStatus');
            if (maxDiff < 1) {
                statusElement.textContent = `✅ 对齐完美！最大差异: ${maxDiff.toFixed(1)}px`;
                statusElement.className = 'success';
                updateStatus('对齐检查完成：完美对齐！', 'success');
            } else {
                statusElement.textContent = `❌ 存在对齐问题，最大差异: ${maxDiff.toFixed(1)}px`;
                statusElement.className = 'error';
                updateStatus(`对齐检查完成：存在 ${maxDiff.toFixed(1)}px 的差异`, 'error');
            }
            
            // 在控制台输出详细信息
            console.log('对齐检查结果:', alignmentResults);
        }
        
        // 刷新甘特图
        function refreshGantt() {
            initGantt();
        }
        
        // 更新状态
        function updateStatus(message, type = '') {
            const statusBar = document.getElementById('statusBar');
            statusBar.textContent = message;
            statusBar.className = 'status ' + type;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initGantt();
        });
    </script>
</body>
</html>
