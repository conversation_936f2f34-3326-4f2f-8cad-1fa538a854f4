
// 导入日期工具、SVG工具、核心组件和样式
import date_utils from './date_utils';
import { $, createSVG } from './svg_utils';
import Arrow from './arrow';
import Bar from './bar';
import Popup from './popup';
import { DEFAULT_OPTIONS, DEFAULT_VIEW_MODES } from './defaults';
import './styles/gantt.css';


// Gantt 主类，负责整个甘特图的渲染、交互、数据管理
export default class Gantt {
    /**
     * 构造函数，初始化甘特图
     * @param {string|HTMLElement|SVGElement} wrapper - 容器选择器或DOM
     * @param {Array} tasks - 任务数组
     * @param {Object} options - 配置项
     */
    constructor(wrapper, tasks, options) {
        this.setup_wrapper(wrapper);   // 初始化容器和SVG
        this.setup_options(options);   // 初始化配置
        this.setup_tasks(tasks);       // 初始化任务
        this.change_view_mode();       // 设置视图模式
        this.bind_events();            // 绑定事件
    }

    /**
     * 初始化SVG和容器
     */
    setup_wrapper(element) {
        let svg_element, wrapper_element;

        // CSS Selector is passed
        if (typeof element === 'string') {
            let el = document.querySelector(element);
            if (!el) {
                throw new ReferenceError(
                    `CSS selector "${element}" could not be found in DOM`,
                );
            }
            element = el;
        }

        // get the SVGElement
        if (element instanceof HTMLElement) {
            wrapper_element = element;
            svg_element = element.querySelector('svg');
        } else if (element instanceof SVGElement) {
            svg_element = element;
        } else {
            throw new TypeError(
                'Frappe Gantt only supports usage of a string CSS selector,' +
                    " HTML DOM element or SVG DOM element for the 'element' parameter",
            );
        }

        // svg element
        if (!svg_element) {
            // create it
            this.$svg = createSVG('svg', {
                append_to: wrapper_element,
                class: 'gantt',
            });
        } else {
            this.$svg = svg_element;
            this.$svg.classList.add('gantt');
        }

        // wrapper element
        this.$container = this.create_el({
            classes: 'gantt-container',
            append_to: this.$svg.parentElement,
        });

        this.$container.appendChild(this.$svg);
        this.$popup_wrapper = this.create_el({
            classes: 'popup-wrapper',
            append_to: this.$container,
        });
    }

    /**
     * 初始化配置项，合并默认配置
     */
    setup_options(options) {
        this.original_options = options;
        this.options = { ...DEFAULT_OPTIONS, ...options };
        const CSS_VARIABLES = {
            'grid-height': 'container_height',
            'bar-height': 'bar_height',
            'lower-header-height': 'lower_header_height',
            'upper-header-height': 'upper_header_height',
        };
        for (let name in CSS_VARIABLES) {
            let setting = this.options[CSS_VARIABLES[name]];
            if (setting !== 'auto')
                this.$container.style.setProperty(
                    '--gv-' + name,
                    setting + 'px',
                );
        }

        this.config = {
            ignored_dates: [],
            ignored_positions: [],
            extend_by_units: 5, // 减少扩展单位，避免过度扩展
        };

        if (typeof this.options.ignore !== 'function') {
            if (typeof this.options.ignore === 'string')
                this.options.ignore = [this.options.ignord];
            for (let option of this.options.ignore) {
                if (typeof option === 'function') {
                    this.config.ignored_function = option;
                    continue;
                }
                if (typeof option === 'string') {
                    if (option === 'weekend')
                        this.config.ignored_function = (d) =>
                            d.getDay() == 6 || d.getDay() == 0;
                    else this.config.ignored_dates.push(new Date(option + ' '));
                }
            }
        } else {
            this.config.ignored_function = this.options.ignore;
        }
    }

    /**
     * 动态更新配置项
     */
    update_options(options) {
        this.setup_options({ ...this.original_options, ...options });
        this.change_view_mode(undefined, true);
    }

    /**
     * 解析任务数据，补全缺失字段，处理依赖
     */
    setup_tasks(tasks) {
        this.tasks = tasks
            .map((task, i) => {
                if (!task.start) {
                    console.error(
                        `task "${task.id}" doesn't have a start date`,
                    );
                    return false;
                }

                task._start = date_utils.parse(task.start);
                if (task.end === undefined && task.duration !== undefined) {
                    task.end = task._start;
                    let durations = task.duration.split(' ');

                    durations.forEach((tmpDuration) => {
                        let { duration, scale } =
                            date_utils.parse_duration(tmpDuration);
                        task.end = date_utils.add(task.end, duration, scale);
                    });
                }
                if (!task.end) {
                    console.error(`task "${task.id}" doesn't have an end date`);
                    return false;
                }
                task._end = date_utils.parse(task.end);

                let diff = date_utils.diff(task._end, task._start, 'year');
                if (diff < 0) {
                    console.error(
                        `start of task can't be after end of task: in task "${task.id}"`,
                    );
                    return false;
                }

                // make task invalid if duration too large
                if (date_utils.diff(task._end, task._start, 'year') > 10) {
                    console.error(
                        `the duration of task "${task.id}" is too long (above ten years)`,
                    );
                    return false;
                }

                // cache index
                task._index = i;

                // if hours is not set, assume the last day is full day
                // e.g: 2018-09-09 becomes 2018-09-09 23:59:59
                const task_end_values = date_utils.get_date_values(task._end);
                if (task_end_values.slice(3).every((d) => d === 0)) {
                    task._end = date_utils.add(task._end, 24, 'hour');
                }

                // dependencies
                if (
                    typeof task.dependencies === 'string' ||
                    !task.dependencies
                ) {
                    let deps = [];
                    if (task.dependencies) {
                        deps = task.dependencies
                            .split(',')
                            .map((d) => d.trim().replaceAll(' ', '_'))
                            .filter((d) => d);
                    }
                    task.dependencies = deps;
                }

                // uids
                if (!task.id) {
                    task.id = generate_id(task);
                } else if (typeof task.id === 'string') {
                    task.id = task.id.replaceAll(' ', '_');
                } else {
                    task.id = `${task.id}`;
                }

                // 表格树相关属性初始化
                if (this.options.table_tree.enabled) {
                    task._level = task.level || 0;
                    task._parent = task.parent || null;
                    task._children = task.children || [];
                    task._expanded = task.expanded !== false; // 默认展开
                    task._visible = true; // 是否在当前视图中可见
                }

                return task;
            })
            .filter((t) => t);

        // 如果启用表格树，需要处理层级结构
        if (this.options.table_tree.enabled) {
            this.setup_tree_structure();
        }

        this.setup_dependencies();
    }

    /**
     * 设置树形结构，处理父子关系和可见性
     */
    setup_tree_structure() {
        // 建立父子关系映射
        this.task_map = {};
        this.root_tasks = [];

        // 创建任务映射
        for (let task of this.tasks) {
            this.task_map[task.id] = task;
            // 确保 _children 数组存在
            if (!task._children) {
                task._children = [];
            }
        }

        // 从 dependencies 建立父子关系（如果没有明确的 parent 设置）
        for (let task of this.tasks) {
            if (!task._parent && task.dependencies && task.dependencies.length > 0) {
                // 使用第一个依赖作为父任务（简化处理）
                const parentId = task.dependencies[0];
                const parent = this.task_map[parentId];
                if (parent) {
                    task._parent = parentId;
                    task._level = (parent._level || 0) + 1;
                }
            }
        }

        // 建立父子关系
        for (let task of this.tasks) {
            if (task._parent) {
                const parent = this.task_map[task._parent];
                if (parent) {
                    if (!parent._children.includes(task.id)) {
                        parent._children.push(task.id);
                    }
                } else {
                    console.warn(`Parent task "${task._parent}" not found for task "${task.id}"`);
                    task._parent = null;
                    this.root_tasks.push(task);
                }
            } else {
                this.root_tasks.push(task);
            }
        }

        // 更新可见性
        this.update_task_visibility();
    }

    /**
     * 更新任务可见性（基于父任务的展开状态）
     */
    update_task_visibility() {
        if (!this.options.table_tree.enabled) return;

        // 重置所有任务的可见性为false
        for (let task of this.tasks) {
            task._visible = false;
        }

        // 递归检查可见性
        const checkVisibility = (taskId, parentVisible = true, parentExpanded = true) => {
            const task = this.task_map[taskId];
            if (!task) return;

            // 任务可见的条件：父任务可见且父任务展开（或者是根任务）
            task._visible = parentVisible && parentExpanded;

            // 如果当前任务有子任务，递归检查子任务
            if (task._children && task._children.length > 0) {
                // 子任务的可见性取决于：
                // 1. 当前任务是否可见
                // 2. 当前任务是否展开
                const childrenVisible = task._visible;
                const childrenExpanded = task._expanded !== false; // 默认展开

                for (let childId of task._children) {
                    checkVisibility(childId, childrenVisible, childrenExpanded);
                }
            }
        };

        // 从根任务开始检查
        for (let rootTask of this.root_tasks) {
            checkVisibility(rootTask.id, true, true);
        }

        // 更新可见任务列表和索引
        this.visible_tasks = this.tasks.filter(task => task._visible);
        this.visible_tasks.forEach((task, index) => {
            task._visible_index = index;
        });
    }

    /**
     * 构建依赖映射表
     */
    setup_dependencies() {
        this.dependency_map = {};
        for (let t of this.tasks) {
            for (let d of t.dependencies) {
                this.dependency_map[d] = this.dependency_map[d] || [];
                this.dependency_map[d].push(t.id);
            }
        }
    }

    /**
     * 刷新任务和视图
     */
    refresh(tasks) {
        this.setup_tasks(tasks);
        this.change_view_mode();
    }

    /**
     * 更新单个任务并刷新
     */
    update_task(id, new_details) {
        let task = this.tasks.find((t) => t.id === id);
        let bar = this.bars[task._index];
        Object.assign(task, new_details);
        bar.refresh();
    }

    /**
     * 切换任务节点的展开/折叠状态
     */
    toggle_task_node(taskId) {
        if (!this.options.table_tree.enabled) return;

        const task = this.task_map[taskId];
        if (!task || !task._children || task._children.length === 0) return;

        task._expanded = !task._expanded;
        this.update_task_visibility();
        this.refresh_table_tree();
        this.refresh_gantt_chart();

        // 触发事件
        this.trigger_event('task_toggle', [task, task._expanded]);
    }

    /**
     * 展开所有任务节点
     */
    expand_all() {
        if (!this.options.table_tree.enabled) return;

        for (let task of this.tasks) {
            if (task._children && task._children.length > 0) {
                task._expanded = true;
            }
        }
        this.update_task_visibility();
        this.refresh_table_tree();
        this.refresh_gantt_chart();

        this.trigger_event('expand_all');
    }

    /**
     * 折叠所有任务节点
     */
    collapse_all() {
        if (!this.options.table_tree.enabled) return;

        for (let task of this.tasks) {
            if (task._children && task._children.length > 0) {
                task._expanded = false;
            }
        }
        this.update_task_visibility();
        this.refresh_table_tree();
        this.refresh_gantt_chart();

        this.trigger_event('collapse_all');
    }

    /**
     * 刷新表格树显示
     */
    refresh_table_tree() {
        if (!this.options.table_tree.enabled || !this.$table_body) return;

        // 清空现有内容
        this.$table_body.innerHTML = '';

        // 重新渲染
        this.render_table_rows();
    }

    /**
     * 刷新甘特图部分（重新计算可见任务的位置）
     */
    refresh_gantt_chart() {
        if (!this.options.table_tree.enabled) return;

        // 重新渲染bars
        this.make_bars();
        this.make_arrows();
        this.map_arrows_on_bars();
    }

    /**
     * 切换视图模式（天/周/月等）
     */
    change_view_mode(mode = this.options.view_mode, maintain_pos = false) {
        if (typeof mode === 'string') {
            mode = this.options.view_modes.find((d) => d.name === mode);
        }
        let old_pos, old_scroll_op;
        if (maintain_pos) {
            old_pos = this.$container.scrollLeft;
            old_scroll_op = this.options.scroll_to;
            this.options.scroll_to = null;
        }
        this.options.view_mode = mode.name;
        this.config.view_mode = mode;
        this.update_view_scale(mode);
        this.setup_dates(maintain_pos);
        this.render();
        if (maintain_pos) {
            this.$container.scrollLeft = old_pos;
            this.options.scroll_to = old_scroll_op;
        }
        this.trigger_event('view_change', [mode]);
    }

    /**
     * 更新视图缩放参数
     */
    update_view_scale(mode) {
        let { duration, scale } = date_utils.parse_duration(mode.step);
        this.config.step = duration;
        this.config.unit = scale;
        this.config.column_width =
            this.options.column_width || mode.column_width || 45;
        this.$container.style.setProperty(
            '--gv-column-width',
            this.config.column_width + 'px',
        );
        this.config.header_height =
            this.options.lower_header_height +
            this.options.upper_header_height +
            10;
    }

    /**
     * 初始化日期区间
     */
    setup_dates(refresh = false) {
        this.setup_gantt_dates(refresh);
        this.setup_date_values();
    }

    /**
     * 计算甘特图起止日期
     */
    setup_gantt_dates(refresh) {
        let gantt_start, gantt_end;
        if (!this.tasks.length) {
            gantt_start = new Date();
            gantt_end = new Date();
        }

        for (let task of this.tasks) {
            if (!gantt_start || task._start < gantt_start) {
                gantt_start = task._start;
            }
            if (!gantt_end || task._end > gantt_end) {
                gantt_end = task._end;
            }
        }

        gantt_start = date_utils.start_of(gantt_start, this.config.unit);
        gantt_end = date_utils.start_of(gantt_end, this.config.unit);

        if (!refresh) {
            if (!this.options.infinite_padding) {
                if (typeof this.config.view_mode.padding === 'string')
                    this.config.view_mode.padding = [
                        this.config.view_mode.padding,
                        this.config.view_mode.padding,
                    ];

                let [padding_start, padding_end] =
                    this.config.view_mode.padding.map(
                        date_utils.parse_duration,
                    );
                this.gantt_start = date_utils.add(
                    gantt_start,
                    -padding_start.duration,
                    padding_start.scale,
                );
                this.gantt_end = date_utils.add(
                    gantt_end,
                    padding_end.duration,
                    padding_end.scale,
                );
            } else {
                // 智能计算初始padding，避免过多空白日期
                let padding_multiplier;

                // 根据视图模式和任务数量动态调整padding
                if (this.config.unit === 'day') {
                    padding_multiplier = this.options.table_tree.enabled ? 0.5 : 1.5;
                } else if (this.config.unit === 'hour') {
                    padding_multiplier = this.options.table_tree.enabled ? 0.3 : 1;
                } else if (this.config.unit === 'week') {
                    padding_multiplier = this.options.table_tree.enabled ? 0.2 : 0.8;
                } else {
                    padding_multiplier = this.options.table_tree.enabled ? 0.1 : 0.5;
                }

                // 进一步根据任务数量调整
                if (this.tasks.length < 5) {
                    padding_multiplier *= 0.5; // 任务少时减少padding
                } else if (this.tasks.length > 20) {
                    padding_multiplier *= 0.8; // 任务多时适当减少padding
                }

                const extend_units = Math.max(1, Math.floor(this.config.extend_by_units * padding_multiplier));

                this.gantt_start = date_utils.add(
                    gantt_start,
                    -extend_units,
                    this.config.unit,
                );
                this.gantt_end = date_utils.add(
                    gantt_end,
                    extend_units,
                    this.config.unit,
                );
            }
        }
        this.config.date_format =
            this.config.view_mode.date_format || this.options.date_format;
        this.gantt_start.setHours(0, 0, 0, 0);
    }

    /**
     * 生成所有显示的日期数组
     */
    setup_date_values() {
        let cur_date = this.gantt_start;
        this.dates = [cur_date];

        while (cur_date < this.gantt_end) {
            cur_date = date_utils.add(
                cur_date,
                this.config.step,
                this.config.unit,
            );
            this.dates.push(cur_date);
        }
    }

    /**
     * 绑定主事件
     */
    bind_events() {
        this.bind_grid_click();
        this.bind_holiday_labels();
        this.bind_bar_events();
        this.bind_table_tree_scroll();
    }

    /**
     * 主渲染流程
     */
    render() {
        this.clear();
        this.setup_layers();
        this.make_grid();
        this.make_dates();
        this.make_grid_extras();
        this.make_bars();
        this.make_arrows();
        this.map_arrows_on_bars();
        this.set_dimensions();
        // 如果启用表格树，调整SVG的位置和宽度来为表格留出空间
        if (this.options.table_tree.enabled && this.options.table_tree.show_table) {
            this.$svg.style.marginLeft = this.options.table_tree.table_width + 'px';
            this.$svg.style.width = `calc(100% - ${this.options.table_tree.table_width}px)`;

            // 自动滚动到任务开始位置，减少空白区域
            if (!this.options.scroll_to) {
                this.set_scroll_position('start');
            } else {
                this.set_scroll_position(this.options.scroll_to);
            }
        } else {
            this.$svg.style.marginLeft = '0px';
            this.$svg.style.width = '100%';
            this.set_scroll_position(this.options.scroll_to);
        }
    }

    /**
     * 创建SVG分层（网格、箭头、进度、bar）
     */
    setup_layers() {
        this.layers = {};
        const layers = ['grid', 'arrow', 'progress', 'bar'];
        // make group layers
        for (let layer of layers) {
            this.layers[layer] = createSVG('g', {
                class: layer,
                append_to: this.$svg,
            });
        }
        this.$extras = this.create_el({
            classes: 'extras',
            append_to: this.$container,
        });
        this.$adjust = this.create_el({
            classes: 'adjust hide',
            append_to: this.$extras,
            type: 'button',
        });
        this.$adjust.innerHTML = '&larr;';
    }

    /**
     * 绘制主网格
     */
    make_grid() {
        this.make_grid_background();
        this.make_grid_rows();
        this.make_grid_header();
        this.make_side_header();

        // 如果启用表格树，绘制左侧表格
        if (this.options.table_tree.enabled && this.options.table_tree.show_table) {
            this.make_table_tree();
        }
    }

    /**
     * 绘制网格高亮和刻度
     */
    make_grid_extras() {
        this.make_grid_highlights();
        this.make_grid_ticks();
    }

    /**
     * 绘制网格背景
     */
    make_grid_background() {
        const grid_width = this.dates.length * this.config.column_width;

        // 确定要使用的任务数量
        const taskCount = this.options.table_tree.enabled && this.visible_tasks ?
            this.visible_tasks.length : this.tasks.length;

        const grid_height = Math.max(
            this.config.header_height +
                this.options.padding +
                (this.options.bar_height + this.options.padding) *
                    taskCount -
                10,
            this.options.container_height !== 'auto'
                ? this.options.container_height
                : 0,
        );

        createSVG('rect', {
            x: 0,
            y: 0,
            width: grid_width,
            height: grid_height,
            class: 'grid-background',
            append_to: this.$svg,
        });

        $.attr(this.$svg, {
            height: grid_height,
            width: '100%',
        });
        this.grid_height = grid_height;
        if (this.options.container_height === 'auto')
            this.$container.style.height = grid_height + 'px';
    }

    /**
     * 绘制网格行
     */
    make_grid_rows() {
        const rows_layer = createSVG('g', { append_to: this.layers.grid });

        const row_width = this.dates.length * this.config.column_width;
        const row_height = this.options.bar_height + this.options.padding;

        let y = this.config.header_height;
        for (
            let y = this.config.header_height;
            y < this.grid_height;
            y += row_height
        ) {
            createSVG('rect', {
                x: 0,
                y,
                width: row_width,
                height: row_height,
                class: 'grid-row',
                append_to: rows_layer,
            });
        }
    }

    /**
     * 绘制网格头部（上下header）
     */
    make_grid_header() {
        this.$header = this.create_el({
            width: this.dates.length * this.config.column_width,
            classes: 'grid-header',
            append_to: this.$container,
        });

        this.$upper_header = this.create_el({
            classes: 'upper-header',
            append_to: this.$header,
        });
        this.$lower_header = this.create_el({
            classes: 'lower-header',
            append_to: this.$header,
        });
    }

    /**
     * 绘制侧边栏（视图切换、today按钮）
     */
    make_side_header() {
        this.$side_header = this.create_el({ classes: 'side-header' });
        this.$upper_header.prepend(this.$side_header);

        // Create view mode change select
        if (this.options.view_mode_select) {
            const $select = document.createElement('select');
            $select.classList.add('viewmode-select');

            const $el = document.createElement('option');
            $el.selected = true;
            $el.disabled = true;
            $el.textContent = 'Mode';
            $select.appendChild($el);

            for (const mode of this.options.view_modes) {
                const $option = document.createElement('option');
                $option.value = mode.name;
                $option.textContent = mode.name;
                if (mode.name === this.config.view_mode.name)
                    $option.selected = true;
                $select.appendChild($option);
            }

            $select.addEventListener(
                'change',
                function () {
                    this.change_view_mode($select.value, true);
                }.bind(this),
            );
            this.$side_header.appendChild($select);
        }

        // Create today button
        if (this.options.today_button) {
            let $today_button = document.createElement('button');
            $today_button.classList.add('today-button');
            $today_button.textContent = 'Today';
            $today_button.onclick = this.scroll_current.bind(this);
            this.$side_header.prepend($today_button);
            this.$today_button = $today_button;
        }
    }

    /**
     * 绘制左侧表格树
     */
    make_table_tree() {
        // 创建表格容器
        this.$table_tree = this.create_el({
            classes: 'table-tree',
            width: this.options.table_tree.table_width,
            height: this.$container.offsetHeight,
            append_to: this.$container,
        });

        // 创建表格头部
        this.$table_header = this.create_el({
            classes: 'table-header',
            height: this.config.header_height,
            append_to: this.$table_tree,
        });

        // 创建表格主体
        this.$table_body = this.create_el({
            classes: 'table-body',
            height: this.$container.offsetHeight - this.config.header_height,
            append_to: this.$table_tree,
        });

        // 渲染表格头部
        this.render_table_header();

        // 渲染表格行
        this.render_table_rows();
    }

    /**
     * 渲染表格头部
     */
    render_table_header() {
        let left = 0;
        for (let column of this.options.table_tree.columns) {
            const $header_cell = this.create_el({
                classes: 'table-header-cell',
                left: left,
                width: column.width,
                height: this.config.header_height,
                append_to: this.$table_header,
            });
            $header_cell.textContent = column.title;
            left += column.width;
        }
    }

    /**
     * 渲染表格行
     */
    render_table_rows() {
        const tasksToRender = this.options.table_tree.enabled ?
            (this.visible_tasks || this.tasks) : this.tasks;

        let visibleIndex = 0;
        tasksToRender.forEach((task) => {
            if (!task._visible && this.options.table_tree.enabled) return;

            const row_height = this.options.bar_height + this.options.padding;
            const y = visibleIndex * row_height;

            const $row = this.create_el({
                classes: 'table-row',
                top: y,
                height: row_height,
                width: this.options.table_tree.table_width,
                append_to: this.$table_body,
            });
            $row.setAttribute('data-task-id', task.id);

            let left = 0;
            for (let columnIndex = 0; columnIndex < this.options.table_tree.columns.length; columnIndex++) {
                const column = this.options.table_tree.columns[columnIndex];
                const $cell = this.create_el({
                    classes: 'table-cell',
                    left: left,
                    width: column.width,
                    height: row_height,
                    append_to: $row,
                });

                // 渲染单元格内容
                this.render_table_cell($cell, task, column, columnIndex);
                left += column.width;
            }

            visibleIndex++;
        });
    }

    /**
     * 渲染单个表格单元格
     */
    render_table_cell($cell, task, column, columnIndex) {
        let content = '';
        let indent = 0;

        // 如果是第一列（通常是任务名称），添加层级缩进和展开/折叠图标
        if (columnIndex === 0 && this.options.table_tree.enabled) {
            indent = task._level * this.options.table_tree.indent_width;

            // 添加展开/折叠图标
            if (task._children && task._children.length > 0) {
                const $toggle = this.create_el({
                    classes: 'tree-toggle',
                    left: indent,
                    width: this.options.table_tree.indent_width,
                    height: this.options.bar_height,
                    append_to: $cell,
                });
                $toggle.textContent = task._expanded ?
                    this.options.table_tree.collapse_icon :
                    this.options.table_tree.expand_icon;
                $toggle.onclick = () => this.toggle_task_node(task.id);

                indent += this.options.table_tree.indent_width;
            } else {
                indent += this.options.table_tree.indent_width;
            }
        }

        // 获取单元格内容
        switch (column.key) {
            case 'name':
                content = task.name || task.id;
                break;
            case 'start':
                content = date_utils.format(task._start, 'YYYY-MM-DD', this.options.language);
                break;
            case 'end':
                content = date_utils.format(task._end, 'YYYY-MM-DD', this.options.language);
                break;
            case 'duration':
                // 计算任务持续时间（天数）
                const duration_days = date_utils.diff(task._end, task._start, 'day');
                content = duration_days.toString();
                break;
            case 'priority':
                content = task.priority || '';
                break;
            default:
                content = task[column.key] || '';
        }

        // 创建内容元素
        const $content = this.create_el({
            classes: 'cell-content',
            left: indent,
            width: column.width - indent,
            height: this.options.bar_height,
            append_to: $cell,
        });
        $content.textContent = content;
    }

    /**
     * 绘制网格刻度线
     */
    make_grid_ticks() {
        if (this.options.lines === 'none') return;
        let tick_x = 0;
        let tick_y = this.config.header_height;
        let tick_height = this.grid_height - this.config.header_height;

        let $lines_layer = createSVG('g', {
            class: 'lines_layer',
            append_to: this.layers.grid,
        });

        let row_y = this.config.header_height;

        const row_width = this.dates.length * this.config.column_width;
        const row_height = this.options.bar_height + this.options.padding;
        if (this.options.lines !== 'vertical') {
            for (
                let y = this.config.header_height;
                y < this.grid_height;
                y += row_height
            ) {
                createSVG('line', {
                    x1: 0,
                    y1: row_y + row_height,
                    x2: row_width,
                    y2: row_y + row_height,
                    class: 'row-line',
                    append_to: $lines_layer,
                });
                row_y += row_height;
            }
        }
        if (this.options.lines === 'horizontal') return;

        for (let date of this.dates) {
            let tick_class = 'tick';
            if (
                this.config.view_mode.thick_line &&
                this.config.view_mode.thick_line(date)
            ) {
                tick_class += ' thick';
            }

            createSVG('path', {
                d: `M ${tick_x} ${tick_y} v ${tick_height}`,
                class: tick_class,
                append_to: this.layers.grid,
            });

            if (this.view_is('month')) {
                tick_x +=
                    (date_utils.get_days_in_month(date) *
                        this.config.column_width) /
                    30;
            } else if (this.view_is('year')) {
                tick_x +=
                    (date_utils.get_days_in_year(date) *
                        this.config.column_width) /
                    365;
            } else {
                tick_x += this.config.column_width;
            }
        }
    }

    /**
     * 高亮假期/周末
     */
    highlight_holidays() {
        let labels = {};
        if (!this.options.holidays) return;

        for (let color in this.options.holidays) {
            let check_highlight = this.options.holidays[color];
            if (check_highlight === 'weekend')
                check_highlight = (d) => d.getDay() === 0 || d.getDay() === 6;
            let extra_func;

            if (typeof check_highlight === 'object') {
                let f = check_highlight.find((k) => typeof k === 'function');
                if (f) {
                    extra_func = f;
                }
                if (this.options.holidays.name) {
                    let dateObj = new Date(check_highlight.date + ' ');
                    check_highlight = (d) => dateObj.getTime() === d.getTime();
                    labels[dateObj] = check_highlight.name;
                } else {
                    check_highlight = (d) =>
                        this.options.holidays[color]
                            .filter((k) => typeof k !== 'function')
                            .map((k) => {
                                if (k.name) {
                                    let dateObj = new Date(k.date + ' ');
                                    labels[dateObj] = k.name;
                                    return dateObj.getTime();
                                }
                                return new Date(k + ' ').getTime();
                            })
                            .includes(d.getTime());
                }
            }
            for (
                let d = new Date(this.gantt_start);
                d <= this.gantt_end;
                d.setDate(d.getDate() + 1)
            ) {
                if (
                    this.config.ignored_dates.find(
                        (k) => k.getTime() == d.getTime(),
                    ) ||
                    (this.config.ignored_function &&
                        this.config.ignored_function(d))
                )
                    continue;
                if (check_highlight(d) || (extra_func && extra_func(d))) {
                    const x =
                        (date_utils.diff(
                            d,
                            this.gantt_start,
                            this.config.unit,
                        ) /
                            this.config.step) *
                        this.config.column_width;
                    const height = this.grid_height - this.config.header_height;
                    const d_formatted = date_utils
                        .format(d, 'YYYY-MM-DD', this.options.language)
                        .replace(' ', '_');

                    if (labels[d]) {
                        let label = this.create_el({
                            classes: 'holiday-label ' + 'label_' + d_formatted,
                            append_to: this.$extras,
                        });
                        label.textContent = labels[d];
                    }
                    createSVG('rect', {
                        x: Math.round(x),
                        y: this.config.header_height,
                        width:
                            this.config.column_width /
                            date_utils.convert_scales(
                                this.config.view_mode.step,
                                'day',
                            ),
                        height,
                        class: 'holiday-highlight ' + d_formatted,
                        style: `fill: ${color};`,
                        append_to: this.layers.grid,
                    });
                }
            }
        }
    }

    /**
     * Compute the horizontal x-axis distance and associated date for the current date and view.
     *
     * @returns Object containing the x-axis distance and date of the current date, or null if the current date is out of the gantt range.
     */
    /**
     * 高亮当前日期
     */
    highlight_current() {
        const res = this.get_closest_date();
        if (!res) return;

        const [_, el] = res;
        el.classList.add('current-date-highlight');

        const diff_in_units = date_utils.diff(
            new Date(),
            this.gantt_start,
            this.config.unit,
        );

        const left =
            (diff_in_units / this.config.step) * this.config.column_width;

        this.$current_highlight = this.create_el({
            top: this.config.header_height,
            left,
            height: this.grid_height - this.config.header_height,
            classes: 'current-highlight',
            append_to: this.$container,
        });
        this.$current_ball_highlight = this.create_el({
            top: this.config.header_height - 6,
            left: left - 2.5,
            width: 6,
            height: 6,
            classes: 'current-ball-highlight',
            append_to: this.$header,
        });
    }

    /**
     * 绘制忽略区间和当前高亮
     */
    make_grid_highlights() {
        this.highlight_holidays();
        this.config.ignored_positions = [];

        const height =
            (this.options.bar_height + this.options.padding) *
            this.tasks.length;
        this.layers.grid.innerHTML += `<pattern id="diagonalHatch" patternUnits="userSpaceOnUse" width="4" height="4">
          <path d="M-1,1 l2,-2
                   M0,4 l4,-4
                   M3,5 l2,-2"
                style="stroke:grey; stroke-width:0.3" />
        </pattern>`;

        for (
            let d = new Date(this.gantt_start);
            d <= this.gantt_end;
            d.setDate(d.getDate() + 1)
        ) {
            if (
                !this.config.ignored_dates.find(
                    (k) => k.getTime() == d.getTime(),
                ) &&
                (!this.config.ignored_function ||
                    !this.config.ignored_function(d))
            )
                continue;
            let diff =
                date_utils.convert_scales(
                    date_utils.diff(d, this.gantt_start) + 'd',
                    this.config.unit,
                ) / this.config.step;

            this.config.ignored_positions.push(diff * this.config.column_width);
            createSVG('rect', {
                x: diff * this.config.column_width,
                y: this.config.header_height,
                width: this.config.column_width,
                height: height,
                class: 'ignored-bar',
                style: 'fill: url(#diagonalHatch);',
                append_to: this.$svg,
            });
        }

        const highlightDimensions = this.highlight_current(
            this.config.view_mode,
        );

        if (!highlightDimensions) return;
    }

    /**
     * 创建div元素并设置样式
     */
    create_el({ left, top, width, height, id, classes, append_to, type }) {
        let $el = document.createElement(type || 'div');
        for (let cls of classes.split(' ')) $el.classList.add(cls);
        $el.style.top = top + 'px';
        $el.style.left = left + 'px';
        if (id) $el.id = id;
        if (width) $el.style.width = width + 'px';
        if (height) $el.style.height = height + 'px';
        if (append_to) append_to.appendChild($el);
        return $el;
    }

    /**
     * 绘制日期文本
     */
    make_dates() {
        this.get_dates_to_draw().forEach((date, i) => {
            if (date.lower_text) {
                let $lower_text = this.create_el({
                    left: date.x,
                    top: date.lower_y,
                    classes: 'lower-text date_' + sanitize(date.formatted_date),
                    append_to: this.$lower_header,
                });
                $lower_text.innerText = date.lower_text;
            }

            if (date.upper_text) {
                let $upper_text = this.create_el({
                    left: date.x,
                    top: date.upper_y,
                    classes: 'upper-text',
                    append_to: this.$upper_header,
                });
                $upper_text.innerText = date.upper_text;
            }
        });
        this.upperTexts = Array.from(
            this.$container.querySelectorAll('.upper-text'),
        );
    }

    /**
     * 获取所有需要绘制的日期信息
     */
    get_dates_to_draw() {
        let last_date_info = null;
        const dates = this.dates.map((date, i) => {
            const d = this.get_date_info(date, last_date_info, i);
            last_date_info = d;
            return d;
        });
        return dates;
    }

    /**
     * 获取单个日期的显示信息
     */
    get_date_info(date, last_date_info) {
        let last_date = last_date_info ? last_date_info.date : null;

        let column_width = this.config.column_width;

        const x = last_date_info
            ? last_date_info.x + last_date_info.column_width
            : 0;

        let upper_text = this.config.view_mode.upper_text;
        let lower_text = this.config.view_mode.lower_text;

        if (!upper_text) {
            this.config.view_mode.upper_text = () => '';
        } else if (typeof upper_text === 'string') {
            this.config.view_mode.upper_text = (date) =>
                date_utils.format(date, upper_text, this.options.language);
        }

        if (!lower_text) {
            this.config.view_mode.lower_text = () => '';
        } else if (typeof lower_text === 'string') {
            this.config.view_mode.lower_text = (date) =>
                date_utils.format(date, lower_text, this.options.language);
        }

        return {
            date,
            formatted_date: sanitize(
                date_utils.format(
                    date,
                    this.config.date_format,
                    this.options.language,
                ),
            ),
            column_width: this.config.column_width,
            x,
            upper_text: this.config.view_mode.upper_text(
                date,
                last_date,
                this.options.language,
            ),
            lower_text: this.config.view_mode.lower_text(
                date,
                last_date,
                this.options.language,
            ),
            upper_y: 17,
            lower_y: this.options.upper_header_height + 5,
        };
    }

    /**
     * 绘制所有任务条
     */
    make_bars() {
        try {
            // 清空现有的bars
            if (this.layers && this.layers.bar) {
                this.layers.bar.innerHTML = '';
            }

            // 确定要渲染的任务列表
            const tasksToRender = this.options.table_tree.enabled ?
                (this.visible_tasks || this.tasks) : this.tasks;

            if (!tasksToRender || tasksToRender.length === 0) {
                console.warn('No tasks to render');
                this.bars = [];
                return;
            }

            this.bars = tasksToRender
                .filter(task => {
                    // 基本验证
                    if (!task || !task.id) return false;
                    // 表格树模式下检查可见性
                    return !this.options.table_tree.enabled || task._visible;
                })
                .map((task) => {
                    try {
                        const bar = new Bar(this, task);
                        if (bar && bar.group && this.layers.bar) {
                            this.layers.bar.appendChild(bar.group);
                            return bar;
                        }
                        return null;
                    } catch (error) {
                        console.warn(`Error creating bar for task ${task.id}:`, error);
                        return null;
                    }
                })
                .filter(Boolean); // 过滤掉null值
        } catch (error) {
            console.error('Error in make_bars:', error);
            this.bars = [];
        }
    }

    /**
     * 绘制所有依赖箭头
     */
    make_arrows() {
        this.arrows = [];

        // 清空现有的箭头
        if (this.layers && this.layers.arrow) {
            this.layers.arrow.innerHTML = '';
        }

        // 确定要渲染的任务列表
        const tasksToRender = this.options.table_tree.enabled ?
            (this.visible_tasks || this.tasks) : this.tasks;

        // 创建任务到bar的映射，只包含可见任务
        const taskToBarMap = {};
        this.bars.forEach(bar => {
            taskToBarMap[bar.task.id] = bar;
        });

        for (let task of tasksToRender) {
            // 只为可见任务创建箭头
            if (this.options.table_tree.enabled && !task._visible) continue;

            let arrows = [];
            arrows = task.dependencies
                .map((task_id) => {
                    const dependency = this.get_task(task_id);
                    if (!dependency) return;

                    // 检查依赖任务和当前任务的bar是否都存在
                    const fromBar = taskToBarMap[dependency.id];
                    const toBar = taskToBarMap[task.id];

                    if (!fromBar || !toBar) return; // 如果任一bar不存在，跳过

                    const arrow = new Arrow(
                        this,
                        fromBar, // from_task
                        toBar, // to_task
                    );
                    this.layers.arrow.appendChild(arrow.element);
                    return arrow;
                })
                .filter(Boolean); // filter falsy values
            this.arrows = this.arrows.concat(arrows);
        }
    }

    /**
     * 关联箭头到bar对象
     */
    map_arrows_on_bars() {
        for (let bar of this.bars) {
            bar.arrows = this.arrows.filter((arrow) => {
                return (
                    arrow.from_task.task.id === bar.task.id ||
                    arrow.to_task.task.id === bar.task.id
                );
            });
        }
    }

    /**
     * 设置SVG宽度自适应
     */
    set_dimensions() {
        const { width: cur_width } = this.$svg.getBoundingClientRect();
        const actual_width = this.$svg.querySelector('.grid .grid-row')
            ? this.$svg.querySelector('.grid .grid-row').getAttribute('width')
            : 0;

        // 计算可用宽度（考虑表格树占用的空间）
        const container_width = this.$container.getBoundingClientRect().width;
        const table_width = (this.options.table_tree.enabled && this.options.table_tree.show_table)
            ? this.options.table_tree.table_width : 0;
        const available_width = container_width - table_width;

        // 设置SVG宽度，但不超过可用宽度
        const target_width = Math.min(actual_width, available_width);
        if (cur_width < target_width) {
            this.$svg.setAttribute('width', target_width);
        }
    }

    /**
     * 检查是否需要扩展日期范围
     */
    should_extend_range(direction) {
        if (!this.tasks.length) return false;

        const task_dates = this.tasks.map(t => ({
            start: t._start.getTime(),
            end: t._end.getTime()
        }));

        const earliest_task = Math.min(...task_dates.map(t => t.start));
        const latest_task = Math.max(...task_dates.map(t => t.end));

        const current_start = this.gantt_start.getTime();
        const current_end = this.gantt_end.getTime();

        // 7天的缓冲时间（毫秒）
        const buffer_time = 7 * 24 * 60 * 60 * 1000;

        if (direction === 'left') {
            // 只有当前范围开始时间接近最早任务时间时才允许扩展
            return current_start <= earliest_task + buffer_time;
        } else if (direction === 'right') {
            // 只有当前范围结束时间接近最晚任务时间时才允许扩展
            return current_end >= latest_task - buffer_time;
        }

        return false;
    }

    /**
     * 滚动到指定日期
     */
    set_scroll_position(date) {
        if (this.options.infinite_padding && (!date || date === 'start')) {
            let [min_start, ..._] = this.get_start_end_positions();
            this.$container.scrollLeft = min_start;
            return;
        }
        if (!date || date === 'start') {
            date = this.gantt_start;
        } else if (date === 'end') {
            date = this.gantt_end;
        } else if (date === 'today') {
            return this.scroll_current();
        } else if (typeof date === 'string') {
            date = date_utils.parse(date);
        }

        // Weird bug where infinite padding results in one day offset in scroll
        // Related to header-body displacement
        const units_since_first_task = date_utils.diff(
            date,
            this.gantt_start,
            this.config.unit,
        );
        const scroll_pos =
            (units_since_first_task / this.config.step) *
            this.config.column_width;

        this.$container.scrollTo({
            left: scroll_pos - this.config.column_width / 6,
            behavior: 'smooth',
        });
    }

    /**
     * 滚动到今天
     */
    scroll_current() {
        let res = this.get_closest_date();
        if (res) this.set_scroll_position(res[0]);
    }

    /**
     * 获取最接近今天的日期和元素
     */
    get_closest_date() {
        let now = new Date();
        if (now < this.gantt_start || now > this.gantt_end) return null;

        let current = new Date(),
            el = this.$container.querySelector(
                '.date_' +
                    sanitize(
                        date_utils.format(
                            current,
                            this.config.date_format,
                            this.options.language,
                        ),
                    ),
            );

        // safety check to prevent infinite loop
        let c = 0;
        while (!el && c < this.config.step) {
            current = date_utils.add(current, -1, this.config.unit);
            el = this.$container.querySelector(
                '.date_' +
                    sanitize(
                        date_utils.format(
                            current,
                            this.config.date_format,
                            this.options.language,
                        ),
                    ),
            );
            c++;
        }
        return [
            new Date(
                date_utils.format(
                    current,
                    this.config.date_format,
                    this.options.language,
                ) + ' ',
            ),
            el,
        ];
    }

    /**
     * 绑定网格点击事件
     */
    bind_grid_click() {
        $.on(
            this.$container,
            'click',
            '.grid-row, .grid-header, .ignored-bar, .holiday-highlight',
            () => {
                this.unselect_all();
                this.hide_popup();
            },
        );
    }

    /**
     * 绑定假期高亮的label事件
     */
    bind_holiday_labels() {
        const $highlights =
            this.$container.querySelectorAll('.holiday-highlight');
        for (let h of $highlights) {
            const label = this.$container.querySelector(
                '.label_' + h.classList[1],
            );
            if (!label) continue;
            let timeout;
            h.onmouseenter = (e) => {
                timeout = setTimeout(() => {
                    label.classList.add('show');
                    label.style.left = (e.offsetX || e.layerX) + 'px';
                    label.style.top = (e.offsetY || e.layerY) + 'px';
                }, 300);
            };

            h.onmouseleave = (e) => {
                clearTimeout(timeout);
                label.classList.remove('show');
            };
        }
    }

    /**
     * 获取所有bar的最小/最大起止位置
     */
    get_start_end_positions() {
        if (!this.bars.length) return [0, 0, 0];
        let { x, width } = this.bars[0].group.getBBox();
        let min_start = x;
        let max_start = x;
        let max_end = x + width;
        Array.prototype.forEach.call(this.bars, function ({ group }, i) {
            let { x, width } = group.getBBox();
            if (x < min_start) min_start = x;
            if (x > max_start) max_start = x;
            if (x + width > max_end) max_end = x + width;
        });
        return [min_start, max_start, max_end];
    }

    /**
     * 绑定bar的拖拽、调整、进度等事件
     */
    bind_bar_events() {
        let is_dragging = false;
        let x_on_start = 0;
        let x_on_scroll_start = 0;
        let y_on_start = 0;
        let is_resizing_left = false;
        let is_resizing_right = false;
        let parent_bar_id = null;
        let bars = []; // instanceof Bar
        this.bar_being_dragged = null;

        const action_in_progress = () =>
            is_dragging || is_resizing_left || is_resizing_right;

        this.$svg.onclick = (e) => {
            if (e.target.classList.contains('grid-row')) this.unselect_all();
        };

        let pos = 0;
        $.on(this.$svg, 'mousemove', '.bar-wrapper, .handle', (e) => {
            if (
                this.bar_being_dragged === false &&
                Math.abs((e.offsetX || e.layerX) - pos) > 10
            )
                this.bar_being_dragged = true;
        });

        $.on(this.$svg, 'mousedown', '.bar-wrapper, .handle', (e, element) => {
            const bar_wrapper = $.closest('.bar-wrapper', element);
            if (element.classList.contains('left')) {
                is_resizing_left = true;
                element.classList.add('visible');
            } else if (element.classList.contains('right')) {
                is_resizing_right = true;
                element.classList.add('visible');
            } else if (element.classList.contains('bar-wrapper')) {
                is_dragging = true;
            }

            if (this.popup) this.popup.hide();

            x_on_start = e.offsetX || e.layerX;
            y_on_start = e.offsetY || e.layerY;

            parent_bar_id = bar_wrapper.getAttribute('data-id');
            let ids;
            if (this.options.move_dependencies) {
                ids = [
                    parent_bar_id,
                    ...this.get_all_dependent_tasks(parent_bar_id),
                ];
            } else {
                ids = [parent_bar_id];
            }
            bars = ids.map((id) => this.get_bar(id)).filter(Boolean); // 过滤掉undefined的bar

            this.bar_being_dragged = false;
            pos = x_on_start;

            bars.forEach((bar) => {
                const $bar = bar.$bar;
                $bar.ox = $bar.getX();
                $bar.oy = $bar.getY();
                $bar.owidth = $bar.getWidth();
                $bar.finaldx = 0;
            });
        });

        if (this.options.infinite_padding) {
            let extended = false;
            $.on(this.$container, 'mousewheel', (e) => {
                // 更严格的触发条件：只在接近边界时才扩展
                const buffer_zone = this.config.column_width * 5; // 5列的缓冲区
                const scroll_left = e.currentTarget.scrollLeft;
                const scroll_width = e.currentTarget.scrollWidth;
                const client_width = e.currentTarget.clientWidth;

                // 向左滚动到边界时扩展左侧
                if (!extended && scroll_left <= buffer_zone && this.should_extend_range('left')) {
                    let old_scroll_left = scroll_left;
                    extended = true;

                    this.gantt_start = date_utils.add(
                        this.gantt_start,
                        -this.config.extend_by_units,
                        this.config.unit,
                    );
                    this.setup_date_values();
                    this.render();
                    e.currentTarget.scrollLeft =
                        old_scroll_left +
                        this.config.column_width * this.config.extend_by_units;
                    setTimeout(() => (extended = false), 500); // 增加防抖时间
                }

                // 向右滚动到边界时扩展右侧
                if (!extended && (scroll_width - (scroll_left + client_width)) <= buffer_zone && this.should_extend_range('right')) {
                    let old_scroll_left = scroll_left;
                    extended = true;
                    this.gantt_end = date_utils.add(
                        this.gantt_end,
                        this.config.extend_by_units,
                        this.config.unit,
                    );
                    this.setup_date_values();
                    this.render();
                    e.currentTarget.scrollLeft = old_scroll_left;
                    setTimeout(() => (extended = false), 500); // 增加防抖时间
                }
            });
        }

        $.on(this.$container, 'scroll', (e) => {
            let localBars = [];
            const ids = this.bars.map(({ group }) =>
                group.getAttribute('data-id'),
            );
            let dx;
            if (x_on_scroll_start) {
                dx = e.currentTarget.scrollLeft - x_on_scroll_start;
            }

            // 处理表格树滚动定位
            if (this.$table_tree && this.options.table_tree.enabled && this.options.table_tree.show_table) {
                // 当容器水平滚动时，保持表格树在固定位置
                this.$table_tree.style.transform = `translateX(${e.currentTarget.scrollLeft}px)`;
            }

            // Calculate current scroll position's upper text
            this.current_date = date_utils.add(
                this.gantt_start,
                (e.currentTarget.scrollLeft / this.config.column_width) *
                    this.config.step,
                this.config.unit,
            );

            let current_upper = this.config.view_mode.upper_text(
                this.current_date,
                null,
                this.options.language,
            );
            let $el = this.upperTexts.find(
                (el) => el.textContent === current_upper,
            );

            // 安全检查：确保$el存在再访问其属性
            if ($el) {
                // Recalculate for smoother experience
                this.current_date = date_utils.add(
                    this.gantt_start,
                    ((e.currentTarget.scrollLeft + $el.clientWidth) /
                        this.config.column_width) *
                        this.config.step,
                    this.config.unit,
                );
                current_upper = this.config.view_mode.upper_text(
                    this.current_date,
                    null,
                    this.options.language,
                );
                $el = this.upperTexts.find(
                    (el) => el.textContent === current_upper,
                );
            }

            if ($el && $el !== this.$current) {
                if (this.$current)
                    this.$current.classList.remove('current-upper');

                $el.classList.add('current-upper');
                this.$current = $el;
            }

            x_on_scroll_start = e.currentTarget.scrollLeft;
            let [min_start, max_start, max_end] =
                this.get_start_end_positions();

            if (x_on_scroll_start > max_end + 100) {
                this.$adjust.innerHTML = '&larr;';
                this.$adjust.classList.remove('hide');
                this.$adjust.onclick = () => {
                    this.$container.scrollTo({
                        left: max_start,
                        behavior: 'smooth',
                    });
                };
            } else if (
                x_on_scroll_start + e.currentTarget.offsetWidth <
                min_start - 100
            ) {
                this.$adjust.innerHTML = '&rarr;';
                this.$adjust.classList.remove('hide');
                this.$adjust.onclick = () => {
                    this.$container.scrollTo({
                        left: min_start,
                        behavior: 'smooth',
                    });
                };
            } else {
                this.$adjust.classList.add('hide');
            }

            if (dx && this.bars && this.bars.length > 0) {
                try {
                    localBars = ids.map((id) => this.get_bar(id)).filter(Boolean); // 过滤掉undefined的bar
                    if (this.options.auto_move_label && localBars.length > 0) {
                        localBars.forEach((bar) => {
                            if (bar && typeof bar.update_label_position_on_horizontal_scroll === 'function') {
                                bar.update_label_position_on_horizontal_scroll({
                                    x: dx,
                                    sx: e.currentTarget.scrollLeft,
                                });
                            }
                        });
                    }
                } catch (error) {
                    console.warn('Error updating bar positions during scroll:', error);
                }
            }
        });

        $.on(this.$svg, 'mousemove', (e) => {
            if (!action_in_progress()) return;
            const dx = (e.offsetX || e.layerX) - x_on_start;

            bars.forEach((bar) => {
                const $bar = bar.$bar;
                $bar.finaldx = this.get_snap_position(dx, $bar.ox);
                this.hide_popup();
                if (is_resizing_left) {
                    if (parent_bar_id === bar.task.id) {
                        bar.update_bar_position({
                            x: $bar.ox + $bar.finaldx,
                            width: $bar.owidth - $bar.finaldx,
                        });
                    } else {
                        bar.update_bar_position({
                            x: $bar.ox + $bar.finaldx,
                        });
                    }
                } else if (is_resizing_right) {
                    if (parent_bar_id === bar.task.id) {
                        bar.update_bar_position({
                            width: $bar.owidth + $bar.finaldx,
                        });
                    }
                } else if (
                    is_dragging &&
                    !this.options.readonly &&
                    !this.options.readonly_dates
                ) {
                    bar.update_bar_position({ x: $bar.ox + $bar.finaldx });
                }
            });
        });

        document.addEventListener('mouseup', () => {
            is_dragging = false;
            is_resizing_left = false;
            is_resizing_right = false;
            this.$container
                .querySelector('.visible')
                ?.classList?.remove?.('visible');
        });

        $.on(this.$svg, 'mouseup', (e) => {
            this.bar_being_dragged = null;
            bars.forEach((bar) => {
                const $bar = bar.$bar;
                if (!$bar.finaldx) return;
                bar.date_changed();
                bar.compute_progress();
                bar.set_action_completed();
            });
        });

        this.bind_bar_progress();
    }

    /**
     * 绑定进度拖拽事件
     */
    bind_bar_progress() {
        let x_on_start = 0;
        let y_on_start = 0;
        let is_resizing = null;
        let bar = null;
        let $bar_progress = null;
        let $bar = null;

        $.on(this.$svg, 'mousedown', '.handle.progress', (e, handle) => {
            is_resizing = true;
            x_on_start = e.offsetX || e.layerX;
            y_on_start = e.offsetY || e.layerY;

            const $bar_wrapper = $.closest('.bar-wrapper', handle);
            const id = $bar_wrapper.getAttribute('data-id');
            bar = this.get_bar(id);

            $bar_progress = bar.$bar_progress;
            $bar = bar.$bar;

            $bar_progress.finaldx = 0;
            $bar_progress.owidth = $bar_progress.getWidth();
            $bar_progress.min_dx = -$bar_progress.owidth;
            $bar_progress.max_dx = $bar.getWidth() - $bar_progress.getWidth();
        });

        const range_positions = this.config.ignored_positions.map((d) => [
            d,
            d + this.config.column_width,
        ]);

        $.on(this.$svg, 'mousemove', (e) => {
            if (!is_resizing || !$bar_progress) return;
            let now_x = e.offsetX || e.layerX;

            let moving_right = now_x > x_on_start;
            if (moving_right) {
                let k = range_positions.find(
                    ([begin, end]) => now_x >= begin && now_x < end,
                );
                while (k) {
                    now_x = k[1];
                    k = range_positions.find(
                        ([begin, end]) => now_x >= begin && now_x < end,
                    );
                }
            } else {
                let k = range_positions.find(
                    ([begin, end]) => now_x > begin && now_x <= end,
                );
                while (k) {
                    now_x = k[0];
                    k = range_positions.find(
                        ([begin, end]) => now_x > begin && now_x <= end,
                    );
                }
            }

            let dx = now_x - x_on_start;
            if (dx > $bar_progress.max_dx) {
                dx = $bar_progress.max_dx;
            }
            if (dx < $bar_progress.min_dx) {
                dx = $bar_progress.min_dx;
            }

            $bar_progress.setAttribute('width', $bar_progress.owidth + dx);
            $.attr(bar.$handle_progress, 'cx', $bar_progress.getEndX());

            $bar_progress.finaldx = dx;
        });

        $.on(this.$svg, 'mouseup', () => {
            if (!is_resizing || !$bar_progress) {
                is_resizing = false;
                return;
            }
            is_resizing = false;
            if (!$bar_progress.finaldx) {
                $bar_progress = null;
                bar = null;
                $bar = null;
                return;
            }

            $bar_progress.finaldx = 0;
            bar.progress_changed();
            bar.set_action_completed();
            bar = null;
            $bar_progress = null;
            $bar = null;
        });
    }

    /**
     * 绑定表格树滚动事件，确保表格在水平滚动时保持固定位置
     */
    bind_table_tree_scroll() {
        if (!this.options.table_tree.enabled) return;

        // 不再单独绑定滚动事件，而是在主滚动事件中处理表格树定位
        // 这样可以避免多个滚动事件监听器冲突
    }

    /**
     * 获取所有依赖于指定任务的任务id
     */
    get_all_dependent_tasks(task_id) {
        let out = [];
        let to_process = [task_id];
        while (to_process.length) {
            const deps = to_process.reduce((acc, curr) => {
                acc = acc.concat(this.dependency_map[curr]);
                return acc;
            }, []);

            out = out.concat(deps);
            to_process = deps.filter((d) => !to_process.includes(d));
        }

        return out.filter(Boolean);
    }

    /**
     * 计算吸附后的拖拽距离
     */
    get_snap_position(dx, ox) {
        let unit_length = 1;
        const default_snap =
            this.options.snap_at || this.config.view_mode.snap_at || '1d';

        if (default_snap !== 'unit') {
            const { duration, scale } = date_utils.parse_duration(default_snap);
            unit_length =
                date_utils.convert_scales(this.config.view_mode.step, scale) /
                duration;
        }

        const rem = dx % (this.config.column_width / unit_length);

        let final_dx =
            dx -
            rem +
            (rem < (this.config.column_width / unit_length) * 2
                ? 0
                : this.config.column_width / unit_length);
        let final_pos = ox + final_dx;

        const drn = final_dx > 0 ? 1 : -1;
        let ignored_regions = this.get_ignored_region(final_pos, drn);
        while (ignored_regions.length) {
            final_pos += this.config.column_width * drn;
            ignored_regions = this.get_ignored_region(final_pos, drn);
            if (!ignored_regions.length)
                final_pos -= this.config.column_width * drn;
        }
        return final_pos - ox;
    }

    /**
     * 判断某位置是否在忽略区间
     */
    get_ignored_region(pos, drn = 1) {
        if (drn === 1) {
            return this.config.ignored_positions.filter((val) => {
                return pos > val && pos <= val + this.config.column_width;
            });
        } else {
            return this.config.ignored_positions.filter(
                (val) => pos >= val && pos < val + this.config.column_width,
            );
        }
    }

    /**
     * 取消所有选中和弹窗
     */
    unselect_all() {
        if (this.popup) this.popup.parent.classList.add('hide');
        this.$container
            .querySelectorAll('.date-range-highlight')
            .forEach((k) => k.classList.add('hide'));
    }

    /**
     * 判断当前视图模式
     */
    view_is(modes) {
        if (typeof modes === 'string') {
            return this.config.view_mode.name === modes;
        }

        if (Array.isArray(modes)) {
            return modes.some(view_is);
        }

        return this.config.view_mode.name === modes.name;
    }

    /**
     * 根据id获取任务对象
     */
    get_task(id) {
        return this.tasks.find((task) => {
            return task.id === id;
        });
    }

    /**
     * 根据id获取bar对象
     */
    get_bar(id) {
        return this.bars.find((bar) => {
            return bar.task.id === id;
        });
    }

    /**
     * 显示弹窗
     */
    show_popup(opts) {
        if (this.options.popup === false) return;
        if (!this.popup) {
            this.popup = new Popup(
                this.$popup_wrapper,
                this.options.popup,
                this,
            );
        }
        this.popup.show(opts);
    }

    /**
     * 隐藏弹窗
     */
    hide_popup() {
        this.popup && this.popup.hide();
    }

    /**
     * 触发自定义事件
     */
    trigger_event(event, args) {
        if (this.options['on_' + event]) {
            this.options['on_' + event].apply(this, args);
        }
    }

    /**
     * 获取任务的展开状态
     */
    is_task_expanded(taskId) {
        if (!this.options.table_tree.enabled) return true;
        const task = this.task_map[taskId];
        return task ? task._expanded : true;
    }

    /**
     * 设置任务的展开状态
     */
    set_task_expanded(taskId, expanded) {
        if (!this.options.table_tree.enabled) return;
        const task = this.task_map[taskId];
        if (task && task._children && task._children.length > 0) {
            task._expanded = expanded;
            this.update_task_visibility();
            this.refresh_table_tree();
            this.refresh_gantt_chart();
        }
    }

    /**
     * 获取任务的子任务列表
     */
    get_task_children(taskId) {
        if (!this.options.table_tree.enabled) return [];
        const task = this.task_map[taskId];
        return task ? task._children : [];
    }

    /**
     * 获取任务的父任务
     */
    get_task_parent(taskId) {
        if (!this.options.table_tree.enabled) return null;
        const task = this.task_map[taskId];
        return task ? task._parent : null;
    }

    /**
     * 获取所有可见任务
     */
    get_visible_tasks() {
        return this.options.table_tree.enabled ?
            (this.visible_tasks || this.tasks) : this.tasks;
    }

    /**
     * 切换表格显示/隐藏
     */
    toggle_table() {
        if (!this.options.table_tree.enabled) return;
        this.options.table_tree.show_table = !this.options.table_tree.show_table;
        this.render();
    }

    /**
     * 设置表格宽度
     */
    set_table_width(width) {
        if (!this.options.table_tree.enabled) return;
        this.options.table_tree.table_width = width;
        this.render();
    }

    /**
     * Gets the oldest starting date from the list of tasks
     *
     * @returns Date
     * @memberof Gantt
     */
    /**
     * 获取所有任务中最早的开始日期
     */
    get_oldest_starting_date() {
        if (!this.tasks.length) return new Date();
        return this.tasks
            .map((task) => task._start)
            .reduce((prev_date, cur_date) =>
                cur_date <= prev_date ? cur_date : prev_date,
            );
    }

    /**
     * Clear all elements from the parent svg element
     *
     * @memberof Gantt
     */
    /**
     * 清空SVG和相关元素
     */
    clear() {
        this.$svg.innerHTML = '';
        this.$header?.remove?.();
        this.$side_header?.remove?.();
        this.$current_highlight?.remove?.();
        this.$extras?.remove?.();
        this.popup?.hide?.();
        // 清理表格树元素
        this.$table_tree?.remove?.();
        this.$table_header?.remove?.();
        this.$table_body?.remove?.();
    }
}

// 静态属性：常用视图模式
Gantt.VIEW_MODE = {
    HOUR: DEFAULT_VIEW_MODES[0],
    QUARTER_DAY: DEFAULT_VIEW_MODES[1],
    HALF_DAY: DEFAULT_VIEW_MODES[2],
    DAY: DEFAULT_VIEW_MODES[3],
    WEEK: DEFAULT_VIEW_MODES[4],
    MONTH: DEFAULT_VIEW_MODES[5],
    YEAR: DEFAULT_VIEW_MODES[6],
};


// 生成唯一任务id
function generate_id(task) {
    return task.name + '_' + Math.random().toString(36).slice(2, 12);
}


// 字符串转为安全class名
function sanitize(s) {
    return s.replaceAll(' ', '_').replaceAll(':', '_').replaceAll('.', '_');
}
