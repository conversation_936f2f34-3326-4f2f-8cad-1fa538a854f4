<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对齐修复测试</title>
     <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .gantt-wrapper {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .debug-line {
            position: fixed;
            height: 2px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0.8;
        }
        
        .debug-line::after {
            content: attr(data-label);
            position: absolute;
            right: 5px;
            top: -20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 对齐修复测试</h1>
        <p>验证表格与甘特图的精确对齐</p>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="checkAlignment()">检查对齐</button>
        <button class="btn" onclick="clearDebugLines()">清除调试线</button>
        <button class="btn" onclick="refreshGantt()">刷新甘特图</button>
    </div>
    
    <div id="status" class="status">
        点击"检查对齐"开始测试...
    </div>
    
    <div class="gantt-wrapper">
        <svg id="gantt"></svg>
    </div>

    <!-- <script src="dist/frappe-gantt.js"></script> -->
     <script src="../dist/frappe-gantt.umd.js"></script>
    <script>
        let gantt;
        
        function checkAlignment() {
            const tableRows = document.querySelectorAll('.table-row');
            const ganttBars = document.querySelectorAll('.bar-wrapper');
            
            if (tableRows.length === 0 || ganttBars.length === 0) {
                console.log('等待元素加载...');
                setTimeout(checkAlignment, 100);
                return;
            }

            console.log('检查对齐情况...');
            console.log('表格行数:', tableRows.length);
            console.log('甘特图任务条数:', ganttBars.length);

            let maxDiff = 0;
            let alignmentIssues = [];

            // 获取甘特图容器和SVG的详细位置信息
            const ganttContainer = document.querySelector('.gantt-container');
            const ganttSvg = document.querySelector('.gantt');
            const tableTree = document.querySelector('.table-tree');
            
            const containerRect = ganttContainer.getBoundingClientRect();
            const svgRect = ganttSvg.getBoundingClientRect();
            const tableRect = tableTree ? tableTree.getBoundingClientRect() : null;
            
            console.log('甘特图容器位置:', containerRect);
            console.log('甘特图SVG位置:', svgRect);
            console.log('表格树位置:', tableRect);
            console.log('SVG相对容器偏移:', {
                x: svgRect.left - containerRect.left,
                y: svgRect.top - containerRect.top
            });

            // 检查SVG的transform属性
            const svgStyle = window.getComputedStyle(ganttSvg);
            console.log('SVG transform:', svgStyle.transform);
            console.log('SVG position:', svgStyle.position);
            console.log('SVG top:', svgStyle.top);
            console.log('SVG left:', svgStyle.left);

            // 清除之前的调试线
            document.querySelectorAll('.debug-line').forEach(line => line.remove());

            // 绘制头部分割线（绿色）
            const headerHeight = 50; // 根据实际情况调整
            drawDebugLine(0, headerHeight, window.innerWidth, headerHeight, 'green', '头部分割线');

            for (let i = 0; i < Math.min(tableRows.length, ganttBars.length); i++) {
                const tableRow = tableRows[i];
                const ganttBar = ganttBars[i];
                
                // 获取表格行的位置
                const tableRowRect = tableRow.getBoundingClientRect();
                const tableCenterY = tableRowRect.top + tableRowRect.height / 2;
                
                // 获取甘特图任务条的位置
                const barRect = ganttBar.getBoundingClientRect();
                const barCenterY = barRect.top + barRect.height / 2;
                
                const diff = Math.abs(tableCenterY - barCenterY);
                maxDiff = Math.max(maxDiff, diff);
                
                console.log(`任务 ${i}:`);
                console.log(`  表格行中心Y: ${tableCenterY.toFixed(1)}`);
                console.log(`  甘特图条中心Y: ${barCenterY.toFixed(1)}`);
                console.log(`  差异: ${diff.toFixed(1)}px`);
                
                // 获取甘特图任务条的SVG坐标
                const barElement = ganttBar.querySelector('.bar');
                if (barElement) {
                    const svgY = parseFloat(barElement.getAttribute('y'));
                    console.log(`  SVG Y坐标: ${svgY}`);
                    console.log(`  SVG Y + SVG容器偏移: ${svgY + svgRect.top - containerRect.top}`);
                }
                
                if (diff > 1) {
                    alignmentIssues.push({
                        index: i,
                        tableCenterY,
                        barCenterY,
                        diff
                    });
                }
                
                // 绘制调试线
                drawDebugLine(0, tableCenterY, window.innerWidth, tableCenterY, 'red', `表格行${i}`);
                drawDebugLine(0, barCenterY, window.innerWidth, barCenterY, 'blue', `甘特图条${i}`);
            }

            // 更新状态显示
            updateStatus(maxDiff, alignmentIssues);
        }
        
        function drawDebugLine(x1, y1, x2, y2, color, label) {
            const line = document.createElement('div');
            line.className = 'debug-line';
            line.style.left = x1 + 'px';
            line.style.top = y1 + 'px';
            line.style.width = (x2 - x1) + 'px';
            line.style.backgroundColor = color;
            line.setAttribute('data-label', label);
            document.body.appendChild(line);
        }
        
        function clearDebugLines() {
            document.querySelectorAll('.debug-line').forEach(line => line.remove());
        }
        
        function updateStatus(maxDiff, issues) {
            const statusEl = document.getElementById('status');
            
            if (maxDiff <= 1) {
                statusEl.className = 'status success';
                statusEl.innerHTML = '✅ 对齐完美！最大差异: ' + maxDiff.toFixed(1) + 'px';
            } else {
                statusEl.className = 'status error';
                statusEl.innerHTML = '❌ 存在对齐问题，最大差异: ' + maxDiff.toFixed(1) + 'px';
                
                if (issues.length > 0) {
                    statusEl.innerHTML += '<br><br>问题详情:<br>';
                    issues.forEach(issue => {
                        statusEl.innerHTML += `任务${issue.index}: 差异${issue.diff.toFixed(1)}px<br>`;
                    });
                }
            }
        }
        
        function refreshGantt() {
            if (gantt) {
                gantt.refresh(tasks);
            } else {
                initGantt();
            }
        }
        
        // 测试数据
        const tasks = [
            {
                id: 'task1',
                name: '系统架构设计',
                start: '2024-01-05',
                end: '2024-01-12',
                progress: 100,
                assignee: '李明',
                priority: '高'
            },
            {
                id: 'task2',
                name: '前端界面开发',
                start: '2024-01-08',
                end: '2024-01-15',
                progress: 60,
                assignee: 'Alice',
                priority: '高'
            },
            {
                id: 'task3',
                name: '后端API开发',
                start: '2024-01-10',
                end: '2024-01-20',
                progress: 30,
                assignee: 'Bob',
                priority: '中'
            },
            {
                id: 'task4',
                name: '数据库设计',
                start: '2024-01-15',
                end: '2024-01-25',
                progress: 0,
                assignee: 'Bob',
                priority: '中'
            }
        ];
        
        function initGantt() {
            // 确保使用正确的构造函数
            const GanttConstructor = window.Gantt || window.FrappeGantt;
            gantt = new GanttConstructor('#gantt', tasks, {
                header_height: 50,
                column_width: 30,
                step: 24,
                view_modes: ['Quarter Day', 'Half Day', 'Day', 'Week', 'Month'],
                bar_height: 20,
                bar_corner_radius: 3,
                arrow_curve: 5,
                padding: 18,
                view_mode: 'Day',
                date_format: 'YYYY-MM-DD',
                popup_trigger: 'click',
                language: 'zh',
                table_tree: {
                    enabled: true,
                    show_table: true,
                    table_width: 300,
                    columns: [
                        { key: 'name', label: '任务名称', width: 120 },
                        { key: 'assignee', label: '负责人', width: 80 },
                        { key: 'priority', label: '优先级', width: 80 }
                    ],
                    show_scrollbar: false,
                    text_ellipsis: true
                }
            });
        }
        
        // 初始化甘特图
        initGantt();
        
        // 页面加载完成后自动检查对齐
        setTimeout(checkAlignment, 1000);
    </script>
</body>
</html>
